package cs_chat_service

import (
	"fmt"

	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/whatsapp_api"
)

type CsPostWebhookHandler struct {
	DB *metaorm.DB
}

func NewCsPostWebhookHandler(db *metaorm.DB) *CsPostWebhookHandler {
	return &CsPostWebhookHandler{
		DB: db.Copy(),
	}
}

// HandleMessage processes a message after the main webhook handling to create CS-specific entities
func (h *CsPostWebhookHandler) HandleMessage(payload *whatsapp_api.SimplifiedWebhookEvent, conversation *entities.Conversation, message *entities.Message, workspaceId uint) {
	// Check if this conversation is related to a CS conversation
	csConversation, err := h.findCsConversation(conversation, workspaceId)
	if err != nil {
		logger.Error("Failed to find CS conversation for conversation ID %d: %v", conversation.ID, err)
		return
	}

	if csConversation == nil {
		// This conversation is not related to CS, skip processing
		logger.Debug("Conversation %d is not related to CS, skipping CS message creation", conversation.ID)
		return
	}

	// Create CS message from the regular message
	err = h.createCsMessage(csConversation, message, workspaceId)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to create CS message for message ID %d: %v", message.ID, err))
		return
	}

	// Update CS conversation's last message reference if this is from customer
	if h.isCustomerMessage(message, csConversation) {
		err = h.updateCustomerLastMessage(csConversation, message, workspaceId)
		if err != nil {
			logger.Error("Failed to update customer last message for CS conversation ID %d: %v", csConversation.ID, err)
		}
	}

	logger.Info("Successfully processed CS message for conversation %d, message %d", conversation.ID, message.ID)
}

// findCsConversation finds the CS conversation associated with the given conversation
func (h *CsPostWebhookHandler) findCsConversation(conversation *entities.Conversation, workspaceId uint) (*entities.CsConversation, error) {
	csConversation, err := new(entities.CsConversation).Repo(h.DB, workspaceId).FindOne(
		metaorm.Eq("conversation_id", conversation.ID),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to query CS conversation: %w", err)
	}

	return csConversation, nil
}

// createCsMessage creates a CS message entity from the regular message
func (h *CsPostWebhookHandler) createCsMessage(csConversation *entities.CsConversation, message *entities.Message, workspaceId uint) error {
	// For simplicity, we'll create a CS message for each regular message
	// In a more sophisticated system, you might want to batch messages or have different logic
	csMessage := &entities.CsMessage{}
	csMessage.CsConversationId.Set(csConversation.ID)
	csMessage.ChatChannelId.Set(csConversation.ChatChannelId.Get())
	csMessage.CustomerId.Set(csConversation.CustomerId.Get())
	csMessage.MessageId.Set(message.ID)

	// If this is a customer message (incoming), leave CustomerServiceId empty
	// If this is a CS agent message (outgoing), it would have CustomerServiceId set
	// For webhook messages, they are typically from customers, so we leave CustomerServiceId empty

	// Save the CS message
	_, err := csMessage.Repo(h.DB, workspaceId).Save(csMessage)
	if err != nil {
		return fmt.Errorf("failed to save CS message: %w", err)
	}

	logger.Debug(fmt.Sprintf("Created CS message ID %d for message ID %d", csMessage.ID, message.ID))
	return nil
}

// isCustomerMessage determines if the message is from a customer (incoming) vs CS agent (outgoing)
func (h *CsPostWebhookHandler) isCustomerMessage(message *entities.Message, csConversation *entities.CsConversation) bool {
	// For WhatsApp webhook messages, they are typically incoming from customers
	// We can determine this by checking if the message has a platform sender ID that matches the customer's phone
	// vs the chat channel's phone number ID

	// If the platform sender ID is empty or doesn't match the channel's phone number ID,
	// it's likely from a customer
	if message.PlatformSenderId.Get() == "" {
		return true
	}

	// Load the chat channel to compare phone number IDs
	chatChannel, err := new(entities.ChatChannel).Repo(h.DB, 0).FindOne(
		metaorm.Eq("id", csConversation.ChatChannelId.Get()),
	)
	if err != nil || chatChannel == nil {
		// If we can't determine, assume it's from customer
		return true
	}

	// If the platform sender ID doesn't match the channel's phone number ID, it's from customer
	return message.PlatformSenderId.Get() != chatChannel.PhoneNumberID.Get()
}

// updateCustomerLastMessage updates the CS conversation's reference to the customer's last message
func (h *CsPostWebhookHandler) updateCustomerLastMessage(csConversation *entities.CsConversation, message *entities.Message, workspaceId uint) error {
	csConversation.CustomerLastMessageId.Set(message.ID)

	_, err := csConversation.Repo(h.DB, workspaceId).Save(csConversation)
	if err != nil {
		return fmt.Errorf("failed to update CS conversation customer last message: %w", err)
	}

	logger.Debug("Updated customer last message for CS conversation %d to message %d", csConversation.ID, message.ID)
	return nil
}
