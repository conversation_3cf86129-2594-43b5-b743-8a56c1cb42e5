package cs_chat_service

import (
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/whatsapp_service"
	"github.com/metadiv-tech/whatsapp_api"
)

var globalMessageManager *messageManager

// RegisterCsPostWebhookHandler registers the customer service post webhook handler
func RegisterCsPostWebhookHandler(db *metaorm.DB) {
	csHandler := NewCsPostWebhookHandler(db)

	// Create a wrapper function that matches the PostWhatsappHandler signature
	handlerFunc := func(payload *whatsapp_api.SimplifiedWebhookEvent, conversation *entities.Conversation, message *entities.Message, workspaceId uint) {
		csHandler.HandleMessage(payload, conversation, message, workspaceId)
	}

	// Register the handler
	whatsapp_service.PostWhatsappService.RegisterPostWhatsappHandler(handlerFunc)
}

// RegisterInstagramPlatformSender registers the Instagram platform sender
func RegisterInstagramPlatformSender(sender interface {
	SendMessage(db *metaorm.DB, message *entities.Message, workspaceId uint) error
}) {
	if globalMessageManager != nil {
		globalMessageManager.instagramPlatformSender = sender
	}
}

// SetGlobalMessageManager stores the global message manager instance for Instagram sender registration
func SetGlobalMessageManager(mm *messageManager) {
	globalMessageManager = mm
}
