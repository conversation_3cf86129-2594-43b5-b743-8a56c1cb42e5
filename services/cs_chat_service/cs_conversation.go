package cs_chat_service

import (
	"fmt"

	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
	"github.com/metadiv-tech/mod_chat/services/chat_service"
)

type CreateCsConversationRequest struct {
	CustomerId        uint   `json:"customer_id" binding:"required"`
	CustomerServiceId uint   `json:"customer_service_id,omitempty"`
	ChatChannelId     uint   `json:"chat_channel_id" binding:"required"`
	Name              string `json:"name,omitempty"`
}

type UpdateCsConversationRequest struct {
	CustomerServiceId *uint  `json:"customer_service_id,omitempty"`
	Pinned            *bool  `json:"pinned,omitempty"`
	ClosedAt          *int64 `json:"closed_at,omitempty"`
}

func NewCsConversationManager(db *metaorm.DB) *csConversationManager {
	return &csConversationManager{
		DB:          db.Copy(),
		chatManager: chat_service.NewChatManager(db),
	}
}

type csConversationManager struct {
	DB          *metaorm.DB
	chatManager interface {
		InitiateConversation(name string, conversationType string) *entities.Conversation
	}
}

func (m *csConversationManager) CreateCsConversation(req *CreateCsConversationRequest, workspaceId uint) (*entities.CsConversationDTO, error) {
	// Validate and get chat channel
	chatChannel, err := m.getChatChannel(req.ChatChannelId, workspaceId)
	if err != nil {
		return nil, err
	}
	if chatChannel == nil {
		return nil, errors.ErrorInvalidPlatform // Reusing this error for invalid channel
	}

	// Create underlying conversation
	conversationName := req.Name
	if conversationName == "" {
		conversationName = fmt.Sprintf("CS Conversation %d", req.CustomerId)
	}

	conversation := m.chatManager.InitiateConversation(
		conversationName,
		configs.ConversationTypeDirect,
	)

	// Set chat channel relation on conversation
	conversation.ChatChannelId.Set(req.ChatChannelId)

	// Save conversation to database
	_, err = conversation.Repo(m.DB).Save(conversation)
	if err != nil {
		return nil, err
	}

	// Create CS conversation
	csConversation := &entities.CsConversation{}
	csConversation.ConversationId.Set(conversation.ID)
	csConversation.ChatChannelId.Set(req.ChatChannelId)
	csConversation.CustomerId.Set(req.CustomerId)
	csConversation.CustomerServiceId.Set(req.CustomerServiceId)
	csConversation.Pinned.Set(false)
	csConversation.CustomerLastMessageId.SetNull()

	// Save CS conversation to database
	_, err = csConversation.Repo(m.DB, workspaceId).Save(csConversation)
	if err != nil {
		return nil, err
	}

	// Load related data for response
	if err := m.loadCsConversationRelations(csConversation); err != nil {
		return nil, err
	}

	return csConversation.DTO(), nil
}

func (m *csConversationManager) DeleteCsConversation(csConversationId uint, workspaceId uint) error {
	// Find the CS conversation first
	csConversation, err := new(entities.CsConversation).Repo(m.DB, workspaceId).FindOne(metaorm.Eq("id", csConversationId))
	if err != nil {
		return err
	}
	if csConversation == nil {
		return errors.ErrorConversationNotFound
	}

	// Get the underlying conversation ID
	conversationId := csConversation.ConversationId.Get()

	// Delete the CS conversation first
	err = csConversation.Repo(m.DB, workspaceId).Delete(csConversation)
	if err != nil {
		return err
	}

	// Delete the underlying conversation
	conversation := &entities.Conversation{}
	conversation.ID = conversationId
	err = conversation.Repo(m.DB).Delete(conversation)
	if err != nil {
		return err
	}

	return nil
}

func (m *csConversationManager) loadCsConversationRelations(csConversation *entities.CsConversation) error {
	// Load conversation
	conversation, err := new(entities.Conversation).Repo(m.DB).FindOne(metaorm.Eq("id", csConversation.ConversationId.Get()))
	if err != nil {
		return err
	}
	csConversation.Conversation = conversation

	// Load chat channel
	chatChannel, err := new(entities.ChatChannel).Repo(m.DB, 0).FindOne(metaorm.Eq("id", csConversation.ChatChannelId.Get()))
	if err != nil {
		return err
	}
	csConversation.ChatChannel = chatChannel

	// Load customer (this would depend on the customer module structure)
	// For now, we'll leave it as nil since we don't have access to customer service

	// Load customer service (this would depend on the workspace user module structure)
	// For now, we'll leave it as nil since we don't have access to workspace user service

	return nil
}

func (m *csConversationManager) getChatChannel(chatChannelId uint, workspaceId uint) (*entities.ChatChannel, error) {
	chatChannel, err := new(entities.ChatChannel).Repo(m.DB, workspaceId).FindOne(metaorm.Eq("id", chatChannelId))
	if err != nil {
		return nil, err
	}
	return chatChannel, nil
}

func (m *csConversationManager) GetCsConversation(id uint, workspaceId uint) (*entities.CsConversationDTO, error) {
	// Find CS conversation
	csConversation, err := new(entities.CsConversation).Repo(m.DB.
		Preload(
			"Conversation",
			"ChatChannel",
			"LastMessage",
			"LastMessage.Message",
			"CustomerLastMessage",
			"CustomerLastMessage.Message",
			"Customer",
			"CustomerService",
			"CustomerService.User",
		), workspaceId).FindOne(metaorm.Eq("id", id))
	if err != nil {
		return nil, err
	}
	if csConversation == nil {
		return nil, errors.ErrorConversationNotFound
	}

	// Load related data
	if err := m.loadCsConversationRelations(csConversation); err != nil {
		return nil, err
	}

	return csConversation.DTO(), nil
}

func (m *csConversationManager) UpdateCsConversation(id uint, workspaceId uint, req *UpdateCsConversationRequest) (*entities.CsConversationDTO, error) {
	// Find existing CS conversation
	csConversation, err := new(entities.CsConversation).Repo(m.DB, workspaceId).FindOne(metaorm.Eq("id", id))
	if err != nil {
		return nil, errors.ErrorConversationNotFound
	}

	// Update fields if provided
	if req.CustomerServiceId != nil {
		csConversation.CustomerServiceId.Set(*req.CustomerServiceId)
	}
	if req.Pinned != nil {
		csConversation.Pinned.Set(*req.Pinned)
	}
	if req.ClosedAt != nil {
		csConversation.ClosedAt.Set(*req.ClosedAt)
	}

	// Save updated CS conversation
	_, err = csConversation.Repo(m.DB, workspaceId).Save(csConversation)
	if err != nil {
		return nil, err
	}

	// Load related data for response
	if err := m.loadCsConversationRelations(csConversation); err != nil {
		return nil, err
	}

	return csConversation.DTO(), nil
}

func (m *csConversationManager) ListCsConversations(workspaceId uint, currentWorkspaceUserId uint, isAdmin bool, filter *string, customerServiceId *uint, sort *metaorm.Sorting, page *metaorm.Pagination) ([]entities.CsConversationDTO, *metaorm.Pagination, error) {

	andBuilder := metaorm.NewAndQBuilder()

	// Apply filter logic if filter is provided
	if filter != nil && *filter != "" {
		err := m.applyConversationFilter(andBuilder, *filter, currentWorkspaceUserId, isAdmin)
		if err != nil {
			return nil, nil, err
		}
	} else {
		// Default filtering logic
		if !isAdmin {
			andBuilder.Add(metaorm.Eq("customer_service_id", currentWorkspaceUserId))
		}

		if customerServiceId != nil {
			andBuilder.Add(metaorm.Eq("customer_service_id", *customerServiceId))
		}
	}

	// For simplicity, get all conversations for the workspace first
	// In a real implementation, you'd build proper filtered queries
	csConversations, page, err := new(entities.CsConversation).Repo(
		m.DB.Preload(
			"Conversation",
			"ChatChannel",
			"Customer",
			"CustomerService",
			"CustomerService.User",
			"CustomerLastMessage",
			"CustomerLastMessage.Message",
			"LastMessage",
			"LastMessage.Message",
		),
		workspaceId).FindAllComplex(page, sort, andBuilder.Build())
	if err != nil {
		return nil, nil, err
	}

	// Load related data for all conversations and convert to DTOs
	var conversationDTOs []entities.CsConversationDTO = make([]entities.CsConversationDTO, 0)
	for _, csConversation := range csConversations {
		if err := m.loadCsConversationRelations(&csConversation); err != nil {
			return nil, nil, err
		}
		conversationDTOs = append(conversationDTOs, *csConversation.DTO())
	}

	return conversationDTOs, page, nil
}

func (m *csConversationManager) ValidateCustomerServiceBelongsToWorkspace(workspaceId uint, customerServiceId uint) (bool, error) {
	// Query for any CS conversation with this customer service ID in the workspace
	// If found, the customer service belongs to this workspace
	csConversation, err := new(entities.CsConversation).Repo(m.DB, workspaceId).FindOne(metaorm.Eq("customer_service_id", customerServiceId))
	if err != nil {
		return false, err
	}

	// If we found a conversation with this customer service ID, it belongs to the workspace
	return csConversation != nil, nil
}

func (m *csConversationManager) applyConversationFilter(andBuilder *metaorm.QBuilder, filter string, currentWorkspaceUserId uint, isAdmin bool) error {
	switch filter {
	case configs.ConversationFilterMyChats:
		// My Chats => assigned chats (customer service)
		// Non-admin users can only see their assigned chats anyway
		andBuilder.Add(metaorm.Eq("customer_service_id", currentWorkspaceUserId))

	case configs.ConversationFilterUnread:
		// Unread conversations - where unread_count > 0
		// This uses the actual unread count maintained by the system
		andBuilder.Add(metaorm.Gt("unread_count", 0))
		// Non-admin users can only see their assigned chats
		if !isAdmin {
			andBuilder.Add(metaorm.Eq("customer_service_id", currentWorkspaceUserId))
		}

	case configs.ConversationFilterSupervised:
		// Supervised chats => assigned chats
		// For admin: all assigned chats, for non-admin: only their assigned chats
		andBuilder.Add(metaorm.IsNotNull("customer_service_id"))
		if !isAdmin {
			andBuilder.Add(metaorm.Eq("customer_service_id", currentWorkspaceUserId))
		}

	case configs.ConversationFilterNonSupervised:
		// Non-supervised chats => unassigned chats
		andBuilder.Add(metaorm.IsNull("customer_service_id"))
		// Non-admin users cannot access unassigned chats, so return empty result
		if !isAdmin {
			andBuilder.Add(metaorm.Eq("id", 0)) // This will return no results
		}

	case configs.ConversationFilterAllChats:
		// All chats => all conversations
		// Non-admin users can only see their assigned chats
		if !isAdmin {
			andBuilder.Add(metaorm.Eq("customer_service_id", currentWorkspaceUserId))
		}
		// Admin users see all chats (no additional filter needed)

	case configs.ConversationFilterClosed:
		// Closed conversations => closed_at is not null
		andBuilder.Add(metaorm.IsNotNull("closed_at"))
		// Non-admin users can only see their assigned chats
		if !isAdmin {
			andBuilder.Add(metaorm.Eq("customer_service_id", currentWorkspaceUserId))
		}

	default:
		return fmt.Errorf("invalid filter: %s", filter)
	}

	return nil
}

func (m *csConversationManager) MarkConversationAsRead(conversationId uint, workspaceId uint, currentUserId uint, isAdmin bool) error {
	// Find the CS conversation
	csConversation, err := new(entities.CsConversation).Repo(m.DB, workspaceId).FindOne(metaorm.Eq("id", conversationId))
	if err != nil {
		return err
	}
	if csConversation == nil {
		return errors.ErrorConversationNotFound
	}

	// Verify current user is the assigned customer service rep or is admin
	if !isAdmin && csConversation.CustomerServiceId.Get() != currentUserId {
		return fmt.Errorf("unauthorized: you are not assigned to this conversation")
	}

	// Mark conversation as read by setting unread count to 0
	csConversation.UnreadCount.Set(0)

	// Save updated CS conversation
	_, err = csConversation.Repo(m.DB, workspaceId).Save(csConversation)
	if err != nil {
		return err
	}

	return nil
}
