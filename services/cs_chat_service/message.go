package cs_chat_service

import (
	"fmt"
	"sort"
	"time"

	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/chat_service"
	"github.com/metadiv-tech/mod_chat/services/whatsapp_service"
)

type SendMessageRequest struct {
	ConversationId   uint                      `json:"conversation_id" binding:"required"`
	MessageType      string                    `json:"message_type" binding:"required"`
	Text             string                    `json:"text,omitempty"`
	Filename         string                    `json:"filename,omitempty"`
	MediaContent     string                    `json:"media_content,omitempty"` // base64 encoded
	Location         *entities.MessageLocation `json:"location,omitempty"`
	ReplyToMessageId *uint                     `json:"reply_to_message_id,omitempty"`
}

type ListMessagesRequest struct {
	ConversationId uint `json:"conversation_id" binding:"required"`
}

type MessageManager interface {
	SendMessage(req *SendMessageRequest, isAdmin bool, currentUserId uint, workspaceId uint) (*entities.CsMessageDTO, error)
	ListMessages(req *ListMessagesRequest, isAdmin bool, currentUserId uint, workspaceId uint, sorting *metaorm.Sorting, pagination *metaorm.Pagination) ([]entities.CsMessageDTO, *metaorm.Pagination, error)
}

func NewMessageManager(db *metaorm.DB) MessageManager {
	mm := &messageManager{
		DB:                      db.Copy(),
		chatManager:             chat_service.NewChatManager(db),
		whatsappPlatformSender:  whatsapp_service.NewPlatformSender(db),
		instagramPlatformSender: nil, // Will be set when Instagram module is initialized
	}
	SetGlobalMessageManager(mm)
	return mm
}

type messageManager struct {
	DB          *metaorm.DB
	chatManager interface {
		InitiateTextMessage(conversationId uint, text string) (*entities.Message, error)
		InitiateImageMessage(conversationId uint, filename string, contentBase64 string) (*entities.Message, error)
		InitiateVideoMessage(conversationId uint, filename string, contentBase64 string) (*entities.Message, error)
		InitiateAudioMessage(conversationId uint, filename string, contentBase64 string) (*entities.Message, error)
		InitiateVoiceMessage(conversationId uint, filename string, contentBase64 string) (*entities.Message, error)
		InitiateFileMessage(conversationId uint, filename string, contentBase64 string) (*entities.Message, error)
		InitiateLocationMessage(conversationId uint, location *entities.MessageLocation) (*entities.Message, error)
	}
	whatsappPlatformSender  whatsapp_service.PlatformSender
	instagramPlatformSender interface {
		SendMessage(db *metaorm.DB, message *entities.Message, workspaceId uint) error
	}
}

func (m *messageManager) SendMessage(req *SendMessageRequest, isAdmin bool, currentUserId uint, workspaceId uint) (*entities.CsMessageDTO, error) {
	// Validate message type
	if !m.isValidMessageType(req.MessageType) {
		return nil, fmt.Errorf("invalid message type: %s", req.MessageType)
	}

	// Validate message content based on type
	if err := m.validateMessageContent(req); err != nil {
		return nil, err
	}

	// Get CS conversation and validate access
	csConversation, err := m.getCsConversationByConversationId(req.ConversationId, workspaceId)
	if err != nil {
		return nil, err
	}

	// Verify current user is the assigned customer service rep
	if !isAdmin && csConversation.CustomerServiceId.Get() != currentUserId {
		return nil, fmt.Errorf("unauthorized: you are not assigned to this conversation")
	}

	// Create corresponding regular message for platform sending first
	message, err := m.createMessage(req)
	if err != nil {
		return nil, fmt.Errorf("failed to create message: %w", err)
	}

	// Set reply reference if provided
	if req.ReplyToMessageId != nil {
		message.ReplyToMessageId.Set(*req.ReplyToMessageId)
	}

	// Set timestamp and save message as pending
	message.Timestamp.Set(time.Now().Unix())
	message.Status.Set(configs.MessageStatusPending)

	_, err = message.Repo(m.DB, workspaceId).Save(message)
	if err != nil {
		return nil, fmt.Errorf("failed to save message: %w", err)
	}

	// Create CS message using the created message
	csMessage, err := m.createCsMessageForCustomerService(req, currentUserId, csConversation, message)
	if err != nil {
		return nil, fmt.Errorf("failed to create CS message: %w", err)
	}

	// Set reply reference if provided
	if req.ReplyToMessageId != nil {
		csMessage.ReplyToMessageId.Set(*req.ReplyToMessageId)
	}

	// Save CS message
	_, err = csMessage.Repo(m.DB, workspaceId).Save(csMessage)
	if err != nil {
		return nil, fmt.Errorf("failed to save CS message: %w", err)
	}

	// Update last message for conversation
	err = m.updateConversationLastMessage(req.ConversationId, message.ID, workspaceId)
	if err != nil {
		return nil, fmt.Errorf("failed to update conversation last message: %w", err)
	}

	// Update last message for CS conversation
	err = m.updateCsConversationLastMessage(csConversation.ID, csMessage.ID, workspaceId)
	if err != nil {
		return nil, fmt.Errorf("failed to update CS conversation last message: %w", err)
	}

	// Start async platform sending process
	switch csConversation.ChatChannel.Platform.Get() {
	case configs.PlatformWhatsApp:
		m.whatsappPlatformSender.SendMessageAsync(message, csConversation.ChatChannel, workspaceId)
	case configs.PlatformInstagram:
		// For Instagram, we'll send synchronously for now
		go func() {
			if m.instagramPlatformSender != nil {
				err := m.instagramPlatformSender.SendMessage(m.DB, message, workspaceId)
				if err != nil {
					logger.Error("Failed to send Instagram message: %v", err)
				}
			}
		}()
	}

	return csMessage.DTO(), nil
}

func (m *messageManager) ListMessages(req *ListMessagesRequest, isAdmin bool, currentUserId uint, workspaceId uint, sorting *metaorm.Sorting, pagination *metaorm.Pagination) ([]entities.CsMessageDTO, *metaorm.Pagination, error) {
	// Get CS conversation and validate access
	csConversation, err := m.getCsConversationByConversationId(req.ConversationId, workspaceId)
	if err != nil {
		return nil, nil, err
	}

	// Verify current user is the assigned customer service rep
	if !isAdmin && csConversation.CustomerServiceId.Get() != currentUserId {
		return nil, nil, fmt.Errorf("unauthorized: you are not assigned to this conversation")
	}

	// Set defaults - metaorm will handle nil values appropriately
	if pagination == nil {
		pagination = &metaorm.Pagination{} // Default pagination will be handled by metaorm
	}

	if sorting == nil {
		sorting = &metaorm.Sorting{} // Default sorting will be handled by metaorm
	}

	// Get CS messages for the conversation with all preloads
	andBuilder := metaorm.NewAndQBuilder()
	andBuilder.Add(metaorm.Eq("conversation_id", req.ConversationId))

	csMessages, page, err := new(entities.CsMessage).Repo(m.DB.
		Preload("Message").
		Preload("ReplyToMessage").
		Preload("Conversation").
		Preload("ChatChannel").
		Preload("Customer").
		Preload("CustomerService").
		Preload("CustomerService.User"), workspaceId).FindAllComplex(pagination, sorting, andBuilder.Build())
	if err != nil {
		return nil, nil, fmt.Errorf("failed to fetch CS messages: %w", err)
	}

	// Convert to DTOs
	var csMessageDTOs []entities.CsMessageDTO
	for _, csMessage := range csMessages {
		csMessageDTOs = append(csMessageDTOs, *csMessage.DTO())
	}

	// Sort messages by timestamp in ascending order
	sort.Slice(csMessageDTOs, func(i, j int) bool {
		msgI := csMessageDTOs[i].Message
		msgJ := csMessageDTOs[j].Message

		if msgI == nil && msgJ == nil {
			return false // treat as equal
		}
		if msgI == nil {
			return true // nil messages come first
		}
		if msgJ == nil {
			return false // non-nil message comes after nil
		}

		return msgI.Timestamp < msgJ.Timestamp
	})

	return csMessageDTOs, page, nil
}

func (m *messageManager) isValidMessageType(messageType string) bool {
	validTypes := []string{
		configs.MessageTypeText,
		configs.MessageTypeImage,
		configs.MessageTypeVideo,
		configs.MessageTypeAudio,
		configs.MessageTypeVoice,
		configs.MessageTypeFile,
		configs.MessageTypeLocation,
	}

	for _, validType := range validTypes {
		if messageType == validType {
			return true
		}
	}
	return false
}

func (m *messageManager) validateMessageContent(req *SendMessageRequest) error {
	switch req.MessageType {
	case configs.MessageTypeText:
		if req.Text == "" {
			return fmt.Errorf("text is required for text messages")
		}
	case configs.MessageTypeImage, configs.MessageTypeVideo, configs.MessageTypeAudio, configs.MessageTypeFile:
		if req.MediaContent == "" {
			return fmt.Errorf("media_content is required for %s messages", req.MessageType)
		}
		if req.Filename == "" {
			return fmt.Errorf("filename is required for %s messages", req.MessageType)
		}
	case configs.MessageTypeLocation:
		if req.Location == nil {
			return fmt.Errorf("location is required for location messages")
		}
		if req.Location.LocLatitude == 0 || req.Location.LocLongitude == 0 {
			return fmt.Errorf("valid latitude and longitude are required for location messages")
		}
	}
	return nil
}

func (m *messageManager) createCsMessageForCustomerService(req *SendMessageRequest, currentUserId uint, csConversation *entities.CsConversation, message *entities.Message) (*entities.CsMessage, error) {
	csMessage := &entities.CsMessage{}
	csMessage.CsConversationId.Set(csConversation.ID)
	csMessage.ChatChannelId.Set(csConversation.ChatChannelId.Get())
	csMessage.CustomerServiceId.Set(currentUserId)
	csMessage.MessageId.Set(message.ID)
	csMessage.Message = message

	switch csConversation.ChatChannel.Platform.Get() {
	case configs.PlatformWhatsApp:
		csMessage.Message.PlatformSenderId.Set(csConversation.ChatChannel.PhoneNumberID.Get())
		csMessage.Message.PlatformRecipientId.Set(csConversation.Customer.PhoneCode.Get() + csConversation.Customer.Phone.Get())
	case configs.PlatformInstagram:
		csMessage.Message.PlatformSenderId.Set(csConversation.ChatChannel.BusinessAccountID.Get())
		csMessage.Message.PlatformRecipientId.Set(csConversation.Customer.InstagramId.Get())
	}
	return csMessage, nil
}

func (m *messageManager) createCsMessageForCustomer(req *SendMessageRequest, csConversation *entities.CsConversation, message *entities.Message) (*entities.CsMessage, error) {
	csMessage := &entities.CsMessage{}
	csMessage.CsConversationId.Set(csConversation.ID)
	csMessage.ChatChannelId.Set(csConversation.ChatChannelId.Get())
	csMessage.CustomerId.Set(csConversation.CustomerId.Get())
	csMessage.MessageId.Set(message.ID)
	csMessage.Message = message

	switch csConversation.ChatChannel.Platform.Get() {
	case configs.PlatformWhatsApp:
		csMessage.Message.PlatformSenderId.Set(csConversation.ChatChannel.PhoneNumberID.Get())
		csMessage.Message.PlatformRecipientId.Set(csConversation.Customer.PhoneCode.Get() + csConversation.Customer.Phone.Get())
	case configs.PlatformInstagram:
		csMessage.Message.PlatformSenderId.Set(csConversation.ChatChannel.BusinessAccountID.Get())
		csMessage.Message.PlatformRecipientId.Set(csConversation.Customer.InstagramId.Get())
	}
	return csMessage, nil
}

func (m *messageManager) createMessage(req *SendMessageRequest) (*entities.Message, error) {
	var message *entities.Message
	var err error

	switch req.MessageType {
	case configs.MessageTypeText:
		message, err = m.chatManager.InitiateTextMessage(req.ConversationId, req.Text)
	case configs.MessageTypeImage:
		message, err = m.chatManager.InitiateImageMessage(req.ConversationId, req.Filename, req.MediaContent)
	case configs.MessageTypeVideo:
		message, err = m.chatManager.InitiateVideoMessage(req.ConversationId, req.Filename, req.MediaContent)
	case configs.MessageTypeAudio:
		message, err = m.chatManager.InitiateAudioMessage(req.ConversationId, req.Filename, req.MediaContent)
	case configs.MessageTypeVoice:
		message, err = m.chatManager.InitiateVoiceMessage(req.ConversationId, req.Filename, req.MediaContent)
	case configs.MessageTypeFile:
		message, err = m.chatManager.InitiateFileMessage(req.ConversationId, req.Filename, req.MediaContent)
	case configs.MessageTypeLocation:
		message, err = m.chatManager.InitiateLocationMessage(req.ConversationId, req.Location)
	default:
		return nil, fmt.Errorf("unsupported message type for sending: %s", req.MessageType)
	}

	return message, err
}

func (m *messageManager) getCsConversationByConversationId(conversationId uint, workspaceId uint) (*entities.CsConversation, error) {
	csConversation, err := new(entities.CsConversation).Repo(m.DB.Preload("ChatChannel").Preload("Customer"), workspaceId).FindOne(
		metaorm.Eq("conversation_id", conversationId),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to find CS conversation: %w", err)
	}
	if csConversation == nil {
		return nil, fmt.Errorf("CS conversation not found")
	}
	if csConversation.ChatChannel == nil || !csConversation.ChatChannel.IsActive.Get() {
		return nil, fmt.Errorf("CS conversation not found")
	}

	return csConversation, nil
}

func (m *messageManager) updateConversationLastMessage(conversationId uint, messageId uint, workspaceId uint) error {
	conversation, err := new(entities.Conversation).Repo(m.DB).FindOne(
		metaorm.Eq("id", conversationId),
	)
	if err != nil {
		return fmt.Errorf("failed to find conversation: %w", err)
	}
	if conversation == nil {
		return fmt.Errorf("conversation not found")
	}

	conversation.LastMessageId.Set(messageId)
	_, err = conversation.Repo(m.DB).Save(conversation)
	if err != nil {
		return fmt.Errorf("failed to update conversation last message: %w", err)
	}

	return nil
}

func (m *messageManager) updateCsConversationLastMessage(csConversationId uint, csMessageId uint, workspaceId uint) error {
	csConversation, err := new(entities.CsConversation).Repo(m.DB, workspaceId).FindOne(
		metaorm.Eq("id", csConversationId),
	)
	if err != nil {
		return fmt.Errorf("failed to find CS conversation: %w", err)
	}
	if csConversation == nil {
		return fmt.Errorf("CS conversation not found")
	}

	csConversation.LastMessageId.Set(csMessageId)
	_, err = csConversation.Repo(m.DB, workspaceId).Save(csConversation)
	if err != nil {
		return fmt.Errorf("failed to update CS conversation last message: %w", err)
	}

	return nil
}
