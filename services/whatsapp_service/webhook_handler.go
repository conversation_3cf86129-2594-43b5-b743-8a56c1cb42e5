package whatsapp_service

import (
	"strings"

	"github.com/metadiv-tech/base64static"
	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
	phoneCodeService "github.com/metadiv-tech/mod_data/services/phone_code_service"
	relationshipEntities "github.com/metadiv-tech/mod_relationship/entities"
	"github.com/metadiv-tech/whatsapp_api"
)

type WebhookHandler interface {
	HandleIncomingMessages(messages []whatsapp_api.SimplifiedMessage) error
	HandleMessageStatuses(statuses []whatsapp_api.SimplifiedMessageStatus) error
	HandleTemplateStatuses(statuses []whatsapp_api.SimplifiedTemplateStatus) error
	HandleWebhookErrors(errors []whatsapp_api.SimplifiedError) error
}

type webhookHandler struct {
	DB             *metaorm.DB
	whatsappSender WhatsAppSender
}

func NewWebhookHandler(db *metaorm.DB) WebhookHandler {
	return &webhookHandler{
		DB:             db.Copy(),
		whatsappSender: NewWhatsAppSender(),
	}
}

func (w *webhookHandler) HandleIncomingMessages(messages []whatsapp_api.SimplifiedMessage) error {
	for _, msg := range messages {

		senderID := msg.FromWaID
		receiverID := msg.PhoneID

		csConversation, msgFromCustomer, err := w.getCsConversation(msg, senderID, receiverID)
		if err != nil {
			return err
		}

		switch msg.MessageType {
		case "text", "image", "video", "audio", "document":
			message, err := w.initMessage(msg, csConversation.ChatChannel, csConversation, senderID, receiverID)
			if err != nil {
				logger.Debug("Failed to init message %s: %v", msg.ID, err)
				continue
			}
			if msgFromCustomer {
				message.CustomerId.Set(csConversation.CustomerId.Get())
				message, err := message.Repo(w.DB, csConversation.WorkspaceId.Get()).Save(message)
				if err != nil {
					logger.Debug("Failed to update message %s: %v", message.ID, err)
					continue
				}
				csConversation.CustomerLastMessageId.Set(message.ID)
				csConversation.LastMessageId.Set(message.ID)

				// Increment unread count for customer messages
				currentCount := csConversation.UnreadCount.Get()
				csConversation.UnreadCount.Set(currentCount + 1)

				csConversation, err = csConversation.Repo(w.DB, csConversation.WorkspaceId.Get()).Save(csConversation)
				if err != nil {
					logger.Debug("Failed to update cs conversation %s: %v", csConversation.ID, err)
					continue
				}
			} else {
				message.CustomerServiceId.Set(csConversation.CustomerServiceId.Get())
				message, err := message.Repo(w.DB, csConversation.WorkspaceId.Get()).Save(message)
				if err != nil {
					logger.Debug("Failed to update message %s: %v", message.ID, err)
					continue
				}
				csConversation.LastMessageId.Set(message.ID)

				// Reset unread count when customer service sends a message
				csConversation.UnreadCount.Set(0)

				csConversation, err = csConversation.Repo(w.DB, csConversation.WorkspaceId.Get()).Save(csConversation)
				if err != nil {
					logger.Debug("Failed to update cs conversation %s: %v", csConversation.ID, err)
					continue
				}
			}
		case "reaction":
			if err := w.processReactionMessage(msg); err != nil {
				logger.Error("Failed to process reaction message %s: %v", msg.ID, err)
			}
		default:
			logger.Warn("Unsupported message type: %s", msg.MessageType)
		}
	}
	return nil
}

func (w *webhookHandler) getCsConversation(msg whatsapp_api.SimplifiedMessage, senderID string, receiverID string) (conversation *entities.CsConversation, msgFromCustomer bool, err error) {
	// Find the existing conversation
	csConversation, err := new(entities.CsConversation).RepoWithoutWorkspace(
		w.DB.Joins("ChatChannel").Joins("Customer").Preload("Conversation")).FindOne(
		metaorm.And(
			metaorm.Eq("*ChatChannel.phone_number_id", receiverID),
			metaorm.Eq("*Customer.whatsapp_id", senderID),
		),
	)
	if err != nil {
		return nil, false, err
	}
	if csConversation != nil {
		return csConversation, true, nil
	}

	csConversation, err = new(entities.CsConversation).RepoWithoutWorkspace(
		w.DB.Joins("ChatChannel").Joins("Customer")).FindOne(
		metaorm.And(
			metaorm.Eq("*ChatChannel.phone_number_id", senderID),
			metaorm.Eq("*Customer.whatsapp_id", receiverID),
		),
	)
	if err != nil {
		return nil, false, err
	}
	if csConversation != nil {
		return csConversation, true, nil
	}

	// Create new conversation
	customerID := ""
	msgFromCustomer = false
	channel, err := new(entities.ChatChannel).RepoWithoutWorkspace(w.DB).FindOne(w.DB.Eq("*phone_number_id", receiverID))
	if err != nil {
		return nil, false, err
	}
	if channel == nil {
		channel, err := new(entities.ChatChannel).RepoWithoutWorkspace(w.DB).FindOne(w.DB.Eq("*phone_number_id", senderID))
		if err != nil {
			return nil, false, err
		}
		if channel == nil {
			return nil, false, errors.ErrChatChannelNotFound
		}
		customerID = receiverID
		msgFromCustomer = false
	} else {
		customerID = senderID
		msgFromCustomer = true
	}

	customer, err := new(relationshipEntities.Customer).Repo(w.DB, channel.WorkspaceId.Get()).FindOne(w.DB.Eq("*whatsapp_id", customerID))
	if err != nil {
		return nil, false, err
	}
	if customer == nil {
		customer = &relationshipEntities.Customer{}
		customer.WhatsappId.Set(customerID)
		customer.Active.Set(true)
		phoneCode, err := phoneCodeService.ExtractCountryCode(customerID)
		if err != nil {
			return nil, false, err
		}
		customer.PhoneCode.Set(phoneCode)
		customer.Phone.Set(strings.TrimPrefix(customerID, phoneCode))
		if msgFromCustomer {
			customer.DisplayName.Set(msg.FromName)
		} else {
			customer.DisplayName.Set(customerID)
		}
		customer, err = customer.Repo(w.DB, channel.WorkspaceId.Get()).Save(customer)
		if err != nil {
			return nil, false, err
		}
	}

	newConversation := &entities.Conversation{}
	newConversation.UUID.Generate()
	newConversation.ChatChannelId.Set(channel.ID)
	newConversation.Type.Set(configs.ConversationTypeDirect)
	newConversation, err = newConversation.Repo(w.DB).Save(newConversation)
	if err != nil {
		return nil, false, err
	}

	newCsConversation := &entities.CsConversation{}
	newCsConversation.ConversationId.Set(newConversation.ID)
	newCsConversation.ChatChannelId.Set(channel.ID)
	newCsConversation.CustomerId.Set(customer.ID)
	newCsConversation.Pinned.Set(false)
	newCsConversation.CustomerLastMessageId.SetNull()
	newCsConversation.UnreadCount.Set(0) // Initialize unread count to 0
	newCsConversation, err = newCsConversation.Repo(w.DB, channel.WorkspaceId.Get()).Save(newCsConversation)
	if err != nil {
		return nil, false, err
	}

	// Set the conversation relation for later use
	newCsConversation.Conversation = newConversation

	return newCsConversation, msgFromCustomer, nil
}

func (w *webhookHandler) initMessage(
	msg whatsapp_api.SimplifiedMessage,
	channel *entities.ChatChannel,
	csConversation *entities.CsConversation,
	senderId string,
	receiverId string,
) (*entities.CsMessage, error) {
	message := &entities.Message{}
	message.UUID.Generate()
	if msg.MessageType == "document" {
		message.MessageType.Set(configs.MessageTypeFile)
	} else {
		message.MessageType.Set(msg.MessageType)
	}
	message.Timestamp.Set(msg.Timestamp)
	message.PlatformId.Set(msg.ID)
	message.PlatformSenderId.Set(msg.FromWaID)
	message.PlatformSenderId.Set(senderId)
	message.PlatformRecipientId.Set(receiverId)
	message.Status.Set(configs.MessageStatusDelivered)
	message.ConversationId.Set(csConversation.ConversationId.Get())

	message.Text.Set(msg.Text)
	if msg.Location != nil {
		message.Location.Set(msg.Location)
	}
	if msg.MediaID != "" {
		message.Filename.Set(msg.Filename)
		cli, err := channel.WhatsappClient()
		if err != nil {
			return nil, err
		}
		media, err := cli.Media().Download(msg.MediaID)
		if err != nil {
			return nil, err
		}
		base64, err := base64static.FileToBase64(media)
		if err != nil {
			return nil, err
		}
		message.MediaContent.Set(base64)
	}

	var replyToMessage *entities.Message
	if msg.ReplyToID != "" {
		var err error
		replyToMessage, err = new(entities.Message).RepoWithoutWorkspace(w.DB).FindOne(
			metaorm.Eq("platform_id", msg.ReplyToID),
		)
		if err != nil {
			logger.Debug("Failed to find reply to message %s: %v", msg.ReplyToID, err)
			return nil, err
		}
		message.ReplyToMessageId.Set(replyToMessage.ID)
	}

	message, err := message.Repo(w.DB, channel.WorkspaceId.Get()).Save(message)
	if err != nil {
		return nil, err
	}
	csMessage := &entities.CsMessage{}
	csMessage.MessageId.Set(message.ID)
	csMessage.CsConversationId.Set(csConversation.ID)
	csMessage.ChatChannelId.Set(channel.ID)

	if replyToMessage != nil {
		replyToCsMessage, err := new(entities.CsMessage).RepoWithoutWorkspace(w.DB).FindOne(
			metaorm.Eq("message_id", replyToMessage.ID),
		)
		if err != nil {
			logger.Debug("Failed to find reply to cs message %s: %v", replyToMessage.ID, err)
			return nil, err
		}
		csMessage.ReplyToMessageId.Set(replyToCsMessage.ID)
	}

	return csMessage, nil
}

func (w *webhookHandler) HandleMessageStatuses(statuses []whatsapp_api.SimplifiedMessageStatus) error {
	for _, status := range statuses {
		msg, err := new(entities.Message).RepoWithoutWorkspace(w.DB).FindOne(
			metaorm.Eq("platform_id", status.MessageID),
		)
		if err != nil {
			logger.Debug("Failed to find message %s: %v", status.MessageID, err)
			continue
		}
		if msg == nil {
			logger.Debug("Message %s not found", status.MessageID)
			continue
		}
		msg.Status.Set(status.Status)
		msg, err = msg.Repo(w.DB, msg.WorkspaceId.Get()).Save(msg)
		if err != nil {
			logger.Debug("Failed to update message %s: %v", msg.ID, err)
		}
	}
	return nil
}

func (w *webhookHandler) HandleTemplateStatuses(statuses []whatsapp_api.SimplifiedTemplateStatus) error {
	for _, status := range statuses {
		logger.Info("Template status update - ID: %s, Status: %s, Event: %s",
			status.TemplateID, status.Status, status.Event)

		// TODO: Store template status updates in database if needed
		// This could be used for tracking template approval/rejection status
	}
	return nil
}

func (w *webhookHandler) HandleWebhookErrors(errors []whatsapp_api.SimplifiedError) error {
	for _, err := range errors {
		logger.Error("WhatsApp webhook error - Code: %d, Title: %s, Message: %s, Details: %s",
			err.Code, err.Title, err.Message, err.Details)

		// TODO: Implement error alerting/notification system
		// This could include sending alerts to administrators
	}
	return nil
}

func (w *webhookHandler) processReactionMessage(msg whatsapp_api.SimplifiedMessage) error {
	// Find the original message by platform ID
	originalMsg, err := new(entities.Message).RepoWithoutWorkspace(w.DB).FindOne(
		metaorm.Eq("platform_id", msg.ReplyToID),
	)
	if err != nil {
		return err
	}
	if originalMsg == nil {
		return errors.ErrMessageNotFound
	}
	originalMsg.Emojis.Set([]string{msg.Emoji})
	_, err = originalMsg.Repo(w.DB, 0).Save(originalMsg)
	if err != nil {
		return err
	}
	return nil
}
