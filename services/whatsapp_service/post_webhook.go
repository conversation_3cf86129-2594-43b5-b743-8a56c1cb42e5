package whatsapp_service

import (
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/whatsapp_api"
)

type PostWhatsappHandler func(payload *whatsapp_api.SimplifiedWebhookEvent, chat *entities.Conversation, message *entities.Message, workspaceId uint)

type postWebhookService struct {
	PostWhatsappHandlers []PostWhatsappHandler
}

var PostWhatsappService = &postWebhookService{
	PostWhatsappHandlers: make([]PostWhatsappHandler, 0),
}

func (s *postWebhookService) RegisterPostWhatsappHandler(handler PostWhatsappHandler) {
	s.PostWhatsappHandlers = append(s.PostWhatsappHandlers, handler)
}

func (s *postWebhookService) CallPostHandlers(payload *whatsapp_api.SimplifiedWebhookEvent, conversation *entities.Conversation, message *entities.Message, workspaceId uint) {
	for _, handler := range s.PostWhatsappHandlers {
		handler(payload, conversation, message, workspaceId)
	}
}
