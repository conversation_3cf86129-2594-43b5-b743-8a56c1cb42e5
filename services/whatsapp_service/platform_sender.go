package whatsapp_service

import (
	"fmt"

	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
)

// PlatformSender handles sending messages to different platforms
type PlatformSender interface {
	SendMessageAsync(message *entities.Message, chatChannel *entities.ChatChannel, workspaceId uint)
}

type platformSender struct {
	DB             *metaorm.DB
	whatsappSender WhatsAppSender
}

func NewPlatformSender(db *metaorm.DB) PlatformSender {
	p := &platformSender{
		DB:             db.Copy(),
		whatsappSender: NewWhatsAppSender(),
	}

	return p
}

func (p *platformSender) SendMessageAsync(message *entities.Message, chatChannel *entities.ChatChannel, workspaceId uint) {
	go p.sendMessage(message, chatChannel, workspaceId)
}

func (p *platformSender) sendMessage(message *entities.Message, chatChannel *entities.ChatChannel, workspaceId uint) {
	// Use the DB connection from the service
	db := p.DB

	defer func() {
		if r := recover(); r != nil {
			logger.Error("Panic in sendMessage: %v", r)

			// Check current retry count to determine next status
			if message.Retry.Get() >= 3 {
				// Mark as failed
				err := p.whatsappSender.UpdateMessageStatus(db, message.ID, configs.MessageStatusFailed, workspaceId)
				if err != nil {
					logger.Error("Failed to update message status after panic: %v", err)
				}
				logger.Info(fmt.Sprintf("Message ID %d marked as failed after %d attempts", message.ID, message.Retry.Get()))
			} else {
				// Mark as pending for retry
				err := p.whatsappSender.UpdateMessageStatus(db, message.ID, configs.MessageStatusPending, workspaceId)
				if err != nil {
					logger.Error("Failed to update message status to pending after panic: %v", err)
				}
				logger.Info(fmt.Sprintf("Message ID %d marked as pending for retry (attempt %d/3)", message.ID, message.Retry.Get()))
			}
		}
	}()

	var success bool

	// Send message based on platform
	switch chatChannel.Platform.Get() {
	case configs.PlatformWhatsApp:
		success = p.sendViaWhatsApp(db, message, chatChannel, workspaceId)
	case configs.PlatformInstagram:
		logger.Warn("Instagram platform not implemented yet")
		success = false
	case configs.PlatformFacebook:
		logger.Warn("Facebook platform not implemented yet")
		success = false
	case configs.PlatformSlack:
		logger.Warn("Slack platform not implemented yet")
		success = false
	case configs.PlatformInternal:
		// For internal messages, set platform IDs and mark as sent
		message.PlatformSenderId.Set(chatChannel.PhoneNumberID.Get())
		message.Status.Set(configs.MessageStatusSent)

		_, err := message.Repo(db, workspaceId).Save(message)
		if err != nil {
			logger.Error("Failed to update internal message status: %v", err)
			success = false
		} else {
			success = true
		}
	default:
		logger.Warn("Unknown platform: %s", chatChannel.Platform.Get())
		success = false
	}

	// Handle message status based on success/failure
	if success {
		// Message sent successfully, reset retry count if it was being retried
		if message.Retry.Get() > 0 {
			p.resetRetryFields(db, message.ID, workspaceId)
		}
		logger.Info("Message ID %d sent successfully", message.ID)
	} else {
		// Message failed to send, check retry count to determine next status
		if message.Retry.Get() >= 3 {
			// Mark as failed
			p.updateMessageAsFailed(db, message.ID, workspaceId)
			logger.Info(fmt.Sprintf("Message ID %d marked as failed after %d attempts", message.ID, message.Retry.Get()))
		} else {
			// Mark as pending for retry
			p.updateMessageAsPending(db, message.ID, workspaceId)
			logger.Info(fmt.Sprintf("Message ID %d marked as pending for retry (attempt %d/3)", message.ID, message.Retry.Get()))
		}
	}
}

func (p *platformSender) sendViaWhatsApp(db *metaorm.DB, message *entities.Message, chatChannel *entities.ChatChannel, workspaceId uint) bool {
	whatsappMessageId, err := p.whatsappSender.SendMessage(message, chatChannel)
	if err != nil {
		logger.Error("Failed to send WhatsApp message: %v", err)
		return false
	}

	// Update message with WhatsApp message ID, platform sender info and set as sent
	message.PlatformId.Set(whatsappMessageId)
	message.PlatformSenderId.Set(chatChannel.PhoneNumberID.Get())
	message.Status.Set(configs.MessageStatusSent)

	_, err = message.Repo(db, workspaceId).Save(message)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to update message after sending: %v", err))
		return false
	}

	return true
}

func (p *platformSender) resetRetryFields(db *metaorm.DB, messageId uint, workspaceId uint) {
	// Fetch the message
	message, err := new(entities.Message).Repo(db, workspaceId).FindOne(
		metaorm.Eq("id", messageId),
	)
	if err != nil {
		logger.Error("Failed to find message with ID %d: %v", messageId, err)
		return
	}
	if message == nil {
		logger.Error("Message with ID %d not found", messageId)
		return
	}

	// Reset retry fields
	message.Retry.Set(0)
	message.RetryAt.Set(0)

	// Save the message
	_, err = message.Repo(db, workspaceId).Save(message)
	if err != nil {
		logger.Error("Failed to reset retry fields: %v", err)
	}
}

func (p *platformSender) updateMessageAsFailed(db *metaorm.DB, messageId uint, workspaceId uint) {
	// Fetch the message
	message, err := new(entities.Message).Repo(db, workspaceId).FindOne(
		metaorm.Eq("id", messageId),
	)
	if err != nil {
		logger.Error("Failed to find message with ID %d: %v", messageId, err)
		return
	}
	if message == nil {
		logger.Error("Message with ID %d not found", messageId)
		return
	}

	// Mark the message status as failed
	message.Status.Set(configs.MessageStatusFailed)

	// Save the message
	_, err = message.Repo(db, workspaceId).Save(message)
	if err != nil {
		logger.Error("Failed to save message with failed status: %v", err)
	}
}

func (p *platformSender) updateMessageAsPending(db *metaorm.DB, messageId uint, workspaceId uint) {
	// Fetch the message
	message, err := new(entities.Message).Repo(db, workspaceId).FindOne(
		metaorm.Eq("id", messageId),
	)
	if err != nil {
		logger.Error("Failed to find message with ID %d: %v", messageId, err)
		return
	}
	if message == nil {
		logger.Error("Message with ID %d not found", messageId)
		return
	}

	// Mark the message status as pending
	message.Status.Set(configs.MessageStatusPending)

	// Save the message
	_, err = message.Repo(db, workspaceId).Save(message)
	if err != nil {
		logger.Error("Failed to save message with pending status: %v", err)
	}
}
