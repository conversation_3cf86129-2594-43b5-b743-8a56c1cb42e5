package whatsapp_service

import (
	"encoding/base64"
	"fmt"
	"strings"

	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/whatsapp_api"
)

type WhatsAppSender interface {
	SendMessage(message *entities.Message, chatChannel *entities.ChatChannel) (string, error)
	UpdateMessageStatus(db *metaorm.DB, messageId uint, status string, workspaceId uint) error
}

type whatsAppSender struct{}

func NewWhatsAppSender() WhatsAppSender {
	return &whatsAppSender{}
}

// uploadMediaFromBase64 uploads base64 encoded media content and returns the media ID
func (w *whatsAppSender) uploadMediaFromBase64(client *whatsapp_api.Client, base64Content, filename string) (string, error) {
	// Remove data URL prefix if present (e.g., "data:image/jpeg;base64,")
	if commaIndex := strings.Index(base64Content, ","); commaIndex != -1 {
		base64Content = base64Content[commaIndex+1:]
	}

	// Decode base64 content
	mediaBytes, err := base64.StdEncoding.DecodeString(base64Content)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64 media content: %w", err)
	}

	// Upload media to WhatsApp
	uploadResponse, err := client.Media().Upload(filename, mediaBytes)
	if err != nil {
		return "", fmt.Errorf("failed to upload media to WhatsApp: %w", err)
	}

	return uploadResponse.ID, nil
}

func (w *whatsAppSender) SendMessage(message *entities.Message, chatChannel *entities.ChatChannel) (string, error) {
	// Only send to WhatsApp if platform is WhatsApp
	if chatChannel.Platform.Get() != configs.PlatformWhatsApp {
		logger.Warn("Skipping WhatsApp send for platform: %s", chatChannel.Platform.Get())
		return "", fmt.Errorf("platform %s is not supported by WhatsApp sender", chatChannel.Platform.Get())
	}

	// Create WhatsApp client
	client, err := whatsapp_api.NewClient(
		chatChannel.BusinessAccountID.Get(),
		chatChannel.AccessToken.Get(),
		chatChannel.PhoneNumberID.Get(),
	)
	if err != nil {
		return "", fmt.Errorf("failed to create WhatsApp client: %w", err)
	}

	// Send message based on type
	switch message.MessageType.Get() {
	case configs.MessageTypeText:
		response, err := client.Sender(message.PlatformRecipientId.Get()).Text(message.Text.Get())
		if err != nil {
			return "", fmt.Errorf("failed to send WhatsApp text message: %w", err)
		}
		if !response.IsSuccess() {
			return "", fmt.Errorf("WhatsApp text message failed: %s", response.Error.Message)
		}
		return response.GetMessageID(), nil

	case configs.MessageTypeTemplate:
		// Build template request using the message template parameters
		templateReq := &whatsapp_api.SendTemplateRequest{
			TemplateName: message.TemplateName.Get(),
			Language:     message.TemplateLanguage.Get(),
		}

		// Convert template parameters to WhatsApp API format
		if headerParams := message.TemplateHeaderParams.Get(); headerParams != nil {
			templateReq.HeaderParam = make([]whatsapp_api.TemplateParameter, len(*headerParams))
			for i, param := range *headerParams {
				templateReq.HeaderParam[i] = whatsapp_api.TemplateParameter{
					Text: param.Value,
				}
			}
		}

		if bodyParams := message.TemplateBodyParams.Get(); bodyParams != nil {
			templateReq.BodyParam = make([]whatsapp_api.TemplateParameter, len(*bodyParams))
			for i, param := range *bodyParams {
				templateReq.BodyParam[i] = whatsapp_api.TemplateParameter{
					Text: param.Value,
				}
			}
		}

		if footerParams := message.TemplateFooterParams.Get(); footerParams != nil {
			templateReq.FooterParam = make([]whatsapp_api.TemplateParameter, len(*footerParams))
			for i, param := range *footerParams {
				templateReq.FooterParam[i] = whatsapp_api.TemplateParameter{
					Text: param.Value,
				}
			}
		}

		response, err := client.Sender(message.PlatformRecipientId.Get()).Template(templateReq)
		if err != nil {
			return "", fmt.Errorf("failed to send WhatsApp template message: %w", err)
		}
		if !response.IsSuccess() {
			return "", fmt.Errorf("WhatsApp template message failed: %s", response.Error.Message)
		}
		return response.GetMessageID(), nil

	case configs.MessageTypeImage:
		mediaContent := message.MediaContent.Get()
		if mediaContent == "" {
			return "", fmt.Errorf("no media content found for image message")
		}

		filename := message.Filename.Get()
		if filename == "" {
			filename = "image.jpg" // Default filename if not provided
		}

		// Upload media content to get media ID
		mediaId, err := w.uploadMediaFromBase64(client, mediaContent, filename)
		if err != nil {
			return "", err
		}

		caption := message.Text.Get() // Use text field as caption if available
		response, err := client.Sender(message.PlatformRecipientId.Get()).Image(mediaId, caption)
		if err != nil {
			return "", fmt.Errorf("failed to send WhatsApp image message: %w", err)
		}
		if !response.IsSuccess() {
			return "", fmt.Errorf("WhatsApp image message failed: %s", response.Error.Message)
		}
		return response.GetMessageID(), nil

	case configs.MessageTypeVideo:
		mediaContent := message.MediaContent.Get()
		if mediaContent == "" {
			return "", fmt.Errorf("no media content found for video message")
		}

		filename := message.Filename.Get()
		if filename == "" {
			filename = "video.mp4" // Default filename if not provided
		}

		// Upload media content to get media ID
		mediaId, err := w.uploadMediaFromBase64(client, mediaContent, filename)
		if err != nil {
			return "", err
		}

		caption := message.Text.Get() // Use text field as caption if available
		response, err := client.Sender(message.PlatformRecipientId.Get()).Video(mediaId, caption)
		if err != nil {
			return "", fmt.Errorf("failed to send WhatsApp video message: %w", err)
		}
		if !response.IsSuccess() {
			return "", fmt.Errorf("WhatsApp video message failed: %s", response.Error.Message)
		}
		return response.GetMessageID(), nil

	case configs.MessageTypeAudio:
		mediaContent := message.MediaContent.Get()
		if mediaContent == "" {
			return "", fmt.Errorf("no media content found for audio message")
		}

		filename := message.Filename.Get()
		if filename == "" {
			filename = "audio.mp3" // Default filename if not provided
		}

		// Upload media content to get media ID
		mediaId, err := w.uploadMediaFromBase64(client, mediaContent, filename)
		if err != nil {
			return "", err
		}

		response, err := client.Sender(message.PlatformRecipientId.Get()).Audio(mediaId)
		if err != nil {
			return "", fmt.Errorf("failed to send WhatsApp audio message: %w", err)
		}
		if !response.IsSuccess() {
			return "", fmt.Errorf("WhatsApp audio message failed: %s", response.Error.Message)
		}
		return response.GetMessageID(), nil

	case configs.MessageTypeFile:
		mediaContent := message.MediaContent.Get()
		if mediaContent == "" {
			return "", fmt.Errorf("no media content found for file message")
		}

		filename := message.Filename.Get()
		if filename == "" {
			filename = "document.pdf" // Default filename if not provided
		}

		// Upload media content to get media ID
		mediaId, err := w.uploadMediaFromBase64(client, mediaContent, filename)
		if err != nil {
			return "", err
		}

		caption := message.Text.Get() // Use text field as caption if available
		response, err := client.Sender(message.PlatformRecipientId.Get()).Document(mediaId, caption, filename)
		if err != nil {
			return "", fmt.Errorf("failed to send WhatsApp document message: %w", err)
		}
		if !response.IsSuccess() {
			return "", fmt.Errorf("WhatsApp document message failed: %s", response.Error.Message)
		}
		return response.GetMessageID(), nil

	case configs.MessageTypeLocation:
		location := message.Location.Get()
		response, err := client.Sender(message.PlatformRecipientId.Get()).Location(
			fmt.Sprintf("%f", location.LocLatitude),
			fmt.Sprintf("%f", location.LocLongitude),
			location.LocName,
			location.LocAddress,
		)
		if err != nil {
			return "", fmt.Errorf("failed to send WhatsApp location message: %w", err)
		}
		if !response.IsSuccess() {
			return "", fmt.Errorf("WhatsApp location message failed: %s", response.Error.Message)
		}
		return response.GetMessageID(), nil

	default:
		return "", fmt.Errorf("unsupported message type for WhatsApp: %s", message.MessageType.Get())
	}
}

func (w *whatsAppSender) UpdateMessageStatus(db *metaorm.DB, messageId uint, status string, workspaceId uint) error {
	message := &entities.Message{}
	message.ID = messageId
	message.Status.Set(status)

	_, err := message.Repo(db, workspaceId).Save(message)
	if err != nil {
		return fmt.Errorf("failed to update message status: %w", err)
	}
	return nil
}
