package chat_service

import (
	"context"
	"fmt"

	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/whatsapp_api"
)

type CreateChannelRequest struct {
	Platform          string `json:"platform" binding:"required"`
	AccessToken       string `json:"access_token" binding:"required"`
	BusinessAccountID string `json:"business_account_id" binding:"required"`
	PhoneNumberID     string `json:"phone_number_id" binding:"required"`
	AllowAllUsers     bool   `json:"allow_all_users"`
	UserIds           []uint `json:"user_ids"`
}

type UpdateChannelRequest struct {
	Platform          *string `json:"platform,omitempty"`
	AccessToken       *string `json:"access_token,omitempty"`
	BusinessAccountID *string `json:"business_account_id,omitempty"`
	PhoneNumberID     *string `json:"phone_number_id,omitempty"`
	IsActive          *bool   `json:"is_active,omitempty"`
	AllowAllUsers     *bool   `json:"allow_all_users,omitempty"`
	UserIds           []uint  `json:"user_ids,omitempty"`
}

func NewChannelManager(db *metaorm.DB) *channelManager {
	return &channelManager{
		DB: db.Copy(),
	}
}

type channelManager struct {
	DB *metaorm.DB
}

func (m *channelManager) CreateChannel(req *CreateChannelRequest, workspaceId uint) (*entities.ChatChannelDTO, error) {
	// Check if channel already exists
	exists, err := m.checkChannelExists(req.BusinessAccountID, req.PhoneNumberID, workspaceId)
	if err != nil {
		return nil, fmt.Errorf("failed to check channel existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("channel already exists with this business account and phone number")
	}

	// Validate WhatsApp credentials and get account details
	accountInfo, err := m.validateWhatsAppCredentials(req)
	if err != nil {
		return nil, fmt.Errorf("failed to validate WhatsApp credentials: %w", err)
	}

	// Create channel entity
	channel := &entities.ChatChannel{}
	channel.Secret.Generate()
	channel.WorkspaceId.Set(workspaceId)
	channel.Platform.Set(req.Platform)
	channel.AccessToken.Set(req.AccessToken)
	channel.DisplayName.Set(accountInfo.DisplayName)
	channel.BusinessAccountID.Set(req.BusinessAccountID)
	channel.PhoneNumberID.Set(req.PhoneNumberID)
	channel.PhoneNumber.Set(accountInfo.PhoneNumber)
	channel.DisplayPhoneNumber.Set(accountInfo.DisplayPhoneNumber)
	channel.IsActive.Set(true)
	channel.AllowAllUsers.Set(req.AllowAllUsers)

	// Save to database
	channel, err = channel.Repo(m.DB, workspaceId).Save(channel)
	if err != nil {
		return nil, fmt.Errorf("failed to save channel: %w", err)
	}

	// Add users to channel
	err = m.addUsersToChannel(m.DB, channel, req.UserIds)
	if err != nil {
		return nil, fmt.Errorf("failed to add users to channel: %w", err)
	}

	return channel.DTO(), nil
}

func (m *channelManager) addUsersToChannel(db *metaorm.DB, channel *entities.ChatChannel, userIds []uint) error {
	new(entities.ChatChannelUser).Repo.Repo(db).DeleteByQuery(db.Eq("chat_channel_id", channel.ID))

	channelUsers := make([]entities.ChatChannelUser, len(userIds))
	for i, userId := range userIds {
		channelUsers[i].ChatChannelId.Set(channel.ID)
		channelUsers[i].WorkspaceUserId.Set(userId)
	}

	if len(channelUsers) == 0 {
		return nil
	}
	_, err := new(entities.ChatChannelUser).Repo.Repo(db).SaveAll(channelUsers)
	if err != nil {
		return fmt.Errorf("failed to save channel users: %w", err)
	}
	return nil
}

func (m *channelManager) checkChannelExists(businessAccountID, phoneNumberID string, workspaceId uint) (bool, error) {
	channel, err := new(entities.ChatChannel).Repo(m.DB, workspaceId).FindOne(
		metaorm.And(
			metaorm.Eq("business_account_id", businessAccountID),
			metaorm.Eq("phone_number_id", phoneNumberID),
		),
	)
	if err != nil {
		return false, err
	}
	return channel != nil, nil
}

type WhatsAppAccountInfo struct {
	DisplayName        string
	PhoneNumber        string
	DisplayPhoneNumber string
	Status             string
}

func (m *channelManager) validateWhatsAppCredentials(req *CreateChannelRequest) (*WhatsAppAccountInfo, error) {
	// Create WhatsApp client
	client, err := whatsapp_api.NewClient(
		req.BusinessAccountID,
		req.AccessToken,
		req.PhoneNumberID,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create WhatsApp client: %w", err)
	}

	// Get account information to validate credentials
	ctx := context.Background()
	accountInfo, err := client.AccountInfoWithContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get WhatsApp account info: %w", err)
	}

	return &WhatsAppAccountInfo{
		DisplayName:        accountInfo.VerifiedName,
		PhoneNumber:        accountInfo.DisplayPhoneNumber,
		DisplayPhoneNumber: accountInfo.DisplayPhoneNumber,
		Status:             accountInfo.Status,
	}, nil
}

func (m *channelManager) UpdateChannel(channelId uint, req *UpdateChannelRequest, workspaceId uint) (*entities.ChatChannelDTO, error) {
	// Find existing channel
	channel, err := new(entities.ChatChannel).Repo(m.DB, workspaceId).FindOne(metaorm.Eq("id", channelId))
	if err != nil {
		return nil, fmt.Errorf("failed to find channel: %w", err)
	}
	if channel == nil {
		return nil, fmt.Errorf("channel not found")
	}

	// If updating WhatsApp credentials, validate them
	needsValidation := req.AccessToken != nil || req.BusinessAccountID != nil || req.PhoneNumberID != nil
	if needsValidation {
		// Build validation request with current values as fallback
		validationReq := &CreateChannelRequest{
			Platform:          channel.Platform.Get(),
			AccessToken:       channel.AccessToken.Get(),
			BusinessAccountID: channel.BusinessAccountID.Get(),
			PhoneNumberID:     channel.PhoneNumberID.Get(),
		}

		// Override with new values if provided
		if req.Platform != nil {
			validationReq.Platform = *req.Platform
		}
		if req.AccessToken != nil {
			validationReq.AccessToken = *req.AccessToken
		}
		if req.BusinessAccountID != nil {
			validationReq.BusinessAccountID = *req.BusinessAccountID
		}
		if req.PhoneNumberID != nil {
			validationReq.PhoneNumberID = *req.PhoneNumberID
		}

		// Validate credentials
		accountInfo, err := m.validateWhatsAppCredentials(validationReq)
		if err != nil {
			return nil, fmt.Errorf("failed to validate WhatsApp credentials: %w", err)
		}

		// Update with validated account information
		channel.PhoneNumber.Set(accountInfo.PhoneNumber)
		channel.DisplayPhoneNumber.Set(accountInfo.DisplayPhoneNumber)
	}

	// Update fields if provided
	if req.Platform != nil {
		channel.Platform.Set(*req.Platform)
	}
	if req.AccessToken != nil {
		channel.AccessToken.Set(*req.AccessToken)
	}
	if req.BusinessAccountID != nil {
		channel.BusinessAccountID.Set(*req.BusinessAccountID)
	}
	if req.PhoneNumberID != nil {
		channel.PhoneNumberID.Set(*req.PhoneNumberID)
	}
	if req.IsActive != nil {
		channel.IsActive.Set(*req.IsActive)
	}
	if req.AllowAllUsers != nil {
		channel.AllowAllUsers.Set(*req.AllowAllUsers)
	}

	err = m.addUsersToChannel(m.DB, channel, req.UserIds)
	if err != nil {
		return nil, fmt.Errorf("failed to add users to channel: %w", err)
	}

	// Save updated channel
	_, err = channel.Repo(m.DB, workspaceId).Save(channel)
	if err != nil {
		return nil, fmt.Errorf("failed to save channel: %w", err)
	}

	return channel.DTO(), nil
}

func (m *channelManager) DeleteChannel(channelId uint, workspaceId uint) error {
	// Find existing channel
	channel, err := new(entities.ChatChannel).Repo(m.DB, workspaceId).FindOne(metaorm.Eq("id", channelId))
	if err != nil {
		return fmt.Errorf("failed to find channel: %w", err)
	}
	if channel == nil {
		return fmt.Errorf("channel not found")
	}

	// Delete the channel
	err = channel.Repo(m.DB, workspaceId).Delete(channel)
	if err != nil {
		return fmt.Errorf("failed to delete channel: %w", err)
	}

	return nil
}

func (m *channelManager) GetChannel(channelId uint, workspaceId uint) (*entities.ChatChannelDTO, error) {
	// Find channel
	channel, err := new(entities.ChatChannel).Repo(m.DB.Preload("Users", "Users.User"), workspaceId).FindOne(metaorm.Eq("id", channelId))
	if err != nil {
		return nil, fmt.Errorf("failed to find channel: %w", err)
	}
	if channel == nil {
		return nil, fmt.Errorf("channel not found")
	}

	return channel.DTO(), nil
}

func (m *channelManager) ListChannels(workspaceId uint, workspaceUserId uint, platform *string, isActive *string, onlyAllowed bool, sort *metaorm.Sorting, page *metaorm.Pagination) ([]entities.ChatChannelDTO, *metaorm.Pagination, error) {
	andBuilder := metaorm.NewAndQBuilder()

	if platform != nil && *platform != "" {
		andBuilder.Add(metaorm.Eq("platform", *platform))
	}

	if isActive != nil && *isActive != "" {
		andBuilder.Add(metaorm.Eq("is_active", *isActive == "true"))
	}

	channelIds := make([]any, 0)
	if onlyAllowed {
		channelUsers, err := new(entities.ChatChannelUser).Repo.Repo(m.DB).FindAll(metaorm.Eq("workspace_user_id", workspaceUserId))
		if err != nil {
			return nil, nil, fmt.Errorf("failed to list channels: %w", err)
		}
		for i := range channelUsers {
			channelIds = append(channelIds, channelUsers[i].ChatChannelId)
		}
		andBuilder.Add(metaorm.In("id", channelIds))
	}

	// Get channels with filters
	channels, page, err := new(entities.ChatChannel).Repo(m.DB, workspaceId).FindAllComplex(page, sort, andBuilder.Build())
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list channels: %w", err)
	}

	// Convert to DTOs
	var channelDTOs []entities.ChatChannelDTO
	for _, channel := range channels {
		channelDTOs = append(channelDTOs, *channel.DTO())
	}

	return channelDTOs, page, nil
}
