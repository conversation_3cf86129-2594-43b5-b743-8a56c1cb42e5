package chat_service

import (
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
)

func NewChatManager(db *metaorm.DB) *chatManager {
	return &chatManager{
		DB: db.Copy(),
	}
}

type chatManager struct {
	DB *metaorm.DB
}

func (m *chatManager) InitiateConversation(
	name string,
	conversationType string,
) *entities.Conversation {
	conversation := &entities.Conversation{}
	conversation.UUID.Generate()
	conversation.Name.Set(name)
	conversation.Type.Set(conversationType)
	return conversation
}

func (m *chatManager) GetConversation(
	conversationId uint,
) (*entities.Conversation, error) {
	conversation, err := new(entities.Conversation).Repo(m.DB).FindOne(metaorm.Eq("id", conversationId))
	if err != nil {
		return nil, err
	}
	if conversation == nil {
		return nil, errors.ErrorConversationNotFound
	}
	return conversation, nil
}

func (m *chatManager) InitiateTextMessage(
	conversationId uint,
	text string,
) (*entities.Message, error) {
	conversation, err := m.GetConversation(conversationId)
	if err != nil {
		return nil, err
	}
	message := &entities.Message{}
	message.UUID.Generate()
	message.MessageType.Set(configs.MessageTypeText)
	message.ConversationId.Set(conversation.ID)
	message.Text.Set(text)
	message.Status.Set(configs.MessageStatusPending)
	return message, nil
}

func (m *chatManager) InitiateImageMessage(
	conversationId uint,
	filename string,
	contentBase64 string,
) (*entities.Message, error) {
	conversation, err := m.GetConversation(conversationId)
	if err != nil {
		return nil, err
	}
	message := &entities.Message{}
	message.UUID.Generate()
	message.MessageType.Set(configs.MessageTypeImage)
	message.ConversationId.Set(conversation.ID)
	message.Filename.Set(filename)
	message.MediaContent.Set(contentBase64)
	message.Status.Set(configs.MessageStatusPending)
	return message, nil
}

func (m *chatManager) InitiateVideoMessage(
	conversationId uint,
	filename string,
	contentBase64 string,
) (*entities.Message, error) {
	conversation, err := m.GetConversation(conversationId)
	if err != nil {
		return nil, err
	}
	message := &entities.Message{}
	message.UUID.Generate()
	message.MessageType.Set(configs.MessageTypeVideo)
	message.ConversationId.Set(conversation.ID)
	message.Filename.Set(filename)
	message.MediaContent.Set(contentBase64)
	message.Status.Set(configs.MessageStatusPending)
	return message, nil
}

func (m *chatManager) InitiateAudioMessage(
	conversationId uint,
	filename string,
	contentBase64 string,
) (*entities.Message, error) {
	conversation, err := m.GetConversation(conversationId)
	if err != nil {
		return nil, err
	}
	message := &entities.Message{}
	message.UUID.Generate()
	message.MessageType.Set(configs.MessageTypeAudio)
	message.ConversationId.Set(conversation.ID)
	message.Filename.Set(filename)
	message.MediaContent.Set(contentBase64)
	message.Status.Set(configs.MessageStatusPending)
	return message, nil
}

func (m *chatManager) InitiateVoiceMessage(
	conversationId uint,
	filename string,
	contentBase64 string,
) (*entities.Message, error) {
	conversation, err := m.GetConversation(conversationId)
	if err != nil {
		return nil, err
	}
	message := &entities.Message{}
	message.UUID.Generate()
	message.MessageType.Set(configs.MessageTypeAudio)
	message.ConversationId.Set(conversation.ID)
	message.Filename.Set(filename)
	message.MediaContent.Set(contentBase64)
	message.Status.Set(configs.MessageStatusPending)
	return message, nil
}

func (m *chatManager) InitiateFileMessage(
	conversationId uint,
	filename string,
	contentBase64 string,
) (*entities.Message, error) {
	conversation, err := m.GetConversation(conversationId)
	if err != nil {
		return nil, err
	}
	message := &entities.Message{}
	message.UUID.Generate()
	message.MessageType.Set(configs.MessageTypeFile)
	message.ConversationId.Set(conversation.ID)
	message.Filename.Set(filename)
	message.MediaContent.Set(contentBase64)
	message.Status.Set(configs.MessageStatusPending)
	return message, nil
}

func (m *chatManager) InitiateLocationMessage(
	conversationId uint,
	location *entities.MessageLocation,
) (*entities.Message, error) {
	conversation, err := m.GetConversation(conversationId)
	if err != nil {
		return nil, err
	}
	message := &entities.Message{}
	message.UUID.Generate()
	message.MessageType.Set(configs.MessageTypeLocation)
	message.ConversationId.Set(conversation.ID)
	message.Location.Set(location)
	message.Status.Set(configs.MessageStatusPending)
	return message, nil
}
