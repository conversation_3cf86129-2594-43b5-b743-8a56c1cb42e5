package instagram_service

import (
	"github.com/metadiv-tech/instagram_api"
	"github.com/metadiv-tech/mod_chat/entities"
)

type PostInstagramHandler func(payload *instagram_api.WebhookPayload, chat *entities.Conversation, message *entities.Message, workspaceId uint)

type postWebhookService struct {
	PostInstagramHandlers []PostInstagramHandler
}

var PostInstagramService = &postWebhookService{
	PostInstagramHandlers: make([]PostInstagramHandler, 0),
}

func (s *postWebhookService) RegisterPostInstagramHandler(handler PostInstagramHandler) {
	s.PostInstagramHandlers = append(s.PostInstagramHandlers, handler)
}

func (s *postWebhookService) CallPostHandlers(payload *instagram_api.WebhookPayload, conversation *entities.Conversation, message *entities.Message, workspaceId uint) {
	for _, handler := range s.PostInstagramHandlers {
		handler(payload, conversation, message, workspaceId)
	}
}
