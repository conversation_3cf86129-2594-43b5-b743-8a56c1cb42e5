package instagram_service

import (
	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
)

type PlatformSender interface {
	SendMessage(db *metaorm.DB, message *entities.Message, workspaceId uint) error
}

type platformSender struct {
	instagramSender InstagramSender
}

func NewPlatformSender() PlatformSender {
	return &platformSender{
		instagramSender: NewInstagramSender(),
	}
}

func (p *platformSender) SendMessage(db *metaorm.DB, message *entities.Message, workspaceId uint) error {
	// Find the chat channel
	channel, err := new(entities.ChatChannel).Repo(db, workspaceId).FindOne(
		db.Joins("Conversation").Eq("*Conversation.id", message.ConversationId.Get()),
	)
	if err != nil {
		logger.Error("Failed to find chat channel for Instagram message: %v", err)
		return err
	}

	// Check if it's Instagram platform
	if channel.Platform.Get() != configs.PlatformInstagram {
		logger.Error("Message is not for Instagram platform")
		return nil
	}

	// Send the message using Instagram sender
	err = p.instagramSender.SendMessage(db, channel, message)
	if err != nil {
		logger.Error("Failed to send Instagram message: %v", err)
		// Update message status to failed
		message.Status.Set(configs.MessageStatusFailed)
		_, updateErr := message.Repo(db, workspaceId).Save(message)
		if updateErr != nil {
			logger.Error("Failed to update message status to failed: %v", updateErr)
		}
		return err
	}

	// Update message status to sent
	message.Status.Set(configs.MessageStatusSent)
	_, err = message.Repo(db, workspaceId).Save(message)
	if err != nil {
		logger.Error("Failed to update message status to sent: %v", err)
	}

	logger.Debug("Instagram message sent successfully: %s", message.PlatformId.Get())
	return nil
}
