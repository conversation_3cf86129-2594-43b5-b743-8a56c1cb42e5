package instagram_service

import (
	"github.com/metadiv-tech/instagram_api"
	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/entities"
)

type InstagramSender interface {
	SendMessage(db *metaorm.DB, channel *entities.ChatChannel, message *entities.Message) error
}

type instagramSender struct{}

func NewInstagramSender() InstagramSender {
	return &instagramSender{}
}

func (s *instagramSender) SendMessage(db *metaorm.DB, channel *entities.ChatChannel, message *entities.Message) error {
	client, err := s.getInstagramClient(channel)
	if err != nil {
		return err
	}

	switch message.MessageType.Get() {
	case "text":
		err = s.sendTextMessage(client, message)
	case "image":
		err = s.sendImageMessage(client, message)
	case "video":
		err = s.sendVideoMessage(client, message)
	case "audio":
		err = s.sendAudioMessage(client, message)
	default:
		logger.Warn("Unsupported message type for Instagram: %s", message.MessageType.Get())
		return nil
	}

	if err != nil {
		logger.Error("Failed to send Instagram message: %v", err)
		return err
	}

	return nil
}

func (s *instagramSender) getInstagramClient(channel *entities.ChatChannel) (*instagram_api.Client, error) {
	// Create Instagram client using the business account ID and access token
	client := instagram_api.NewClient(channel.BusinessAccountID.Get(), channel.AccessToken.Get())
	return client, nil
}

func (s *instagramSender) sendTextMessage(client *instagram_api.Client, message *entities.Message) error {
	sender := client.Sender(message.PlatformRecipientId.Get())
	resp, err := sender.Text(message.Text.Get())
	if err != nil {
		return err
	}

	// Update message with platform ID
	message.PlatformId.Set(resp.MessageID)

	logger.Debug("Instagram text message sent: %s", resp.MessageID)
	return nil
}

func (s *instagramSender) sendImageMessage(client *instagram_api.Client, message *entities.Message) error {
	sender := client.Sender(message.PlatformRecipientId.Get())
	resp, err := sender.Image(message.MediaContent.Get())
	if err != nil {
		return err
	}

	// Update message with platform ID
	message.PlatformId.Set(resp.MessageID)

	logger.Debug("Instagram image message sent: %s", resp.MessageID)
	return nil
}

func (s *instagramSender) sendVideoMessage(client *instagram_api.Client, message *entities.Message) error {
	sender := client.Sender(message.PlatformRecipientId.Get())
	resp, err := sender.Video(message.MediaContent.Get())
	if err != nil {
		return err
	}

	// Update message with platform ID
	message.PlatformId.Set(resp.MessageID)

	logger.Debug("Instagram video message sent: %s", resp.MessageID)
	return nil
}

func (s *instagramSender) sendAudioMessage(client *instagram_api.Client, message *entities.Message) error {
	sender := client.Sender(message.PlatformRecipientId.Get())
	resp, err := sender.Audio(message.MediaContent.Get())
	if err != nil {
		return err
	}

	// Update message with platform ID
	message.PlatformId.Set(resp.MessageID)

	logger.Debug("Instagram audio message sent: %s", resp.MessageID)
	return nil
}
