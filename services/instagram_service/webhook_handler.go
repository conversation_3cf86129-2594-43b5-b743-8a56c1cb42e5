package instagram_service

import (
	"io"
	"net/http"

	"github.com/metadiv-tech/base64static"
	"github.com/metadiv-tech/instagram_api"
	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
	relationshipEntities "github.com/metadiv-tech/mod_relationship/entities"
	"github.com/metadiv-tech/nanoid"
)

type WebhookHandler interface {
	HandleIncomingMessages(payload *instagram_api.WebhookPayload) error
}

type webhookHandler struct {
	DB              *metaorm.DB
	instagramSender InstagramSender
}

func NewWebhookHandler(db *metaorm.DB) WebhookHandler {
	return &webhookHandler{
		DB:              db.Copy(),
		instagramSender: NewInstagramSender(),
	}
}

func (w *webhookHandler) HandleIncomingMessages(payload *instagram_api.WebhookPayload) error {
	// Extract messages from webhook payload
	messages := instagram_api.ExtractMessagesFromWebhook(payload)

	for _, msg := range messages {
		senderID := msg.SenderID
		receiverID := msg.RecipientID

		csConversation, msgFromCustomer, err := w.getCsConversation(msg, senderID, receiverID)
		if err != nil {
			logger.Error("Failed to get cs conversation: %v", err)
			continue
		}

		// Create the message entity
		csMessage, err := w.initMessage(msg, csConversation.ChatChannel, csConversation, senderID, receiverID)
		if err != nil {
			logger.Debug("Failed to init message %s: %v", msg.MID, err)
			continue
		}

		if msgFromCustomer {
			csMessage.CustomerId.Set(csConversation.CustomerId.Get())
			csMessage, err := csMessage.Repo(w.DB, csConversation.WorkspaceId.Get()).Save(csMessage)
			if err != nil {
				logger.Debug("Failed to update message %s: %v", csMessage.ID, err)
				continue
			}
			csConversation.CustomerLastMessageId.Set(csMessage.ID)
			csConversation.LastMessageId.Set(csMessage.ID)

			// Increment unread count for customer messages
			currentCount := csConversation.UnreadCount.Get()
			csConversation.UnreadCount.Set(currentCount + 1)

			csConversation, err = csConversation.Repo(w.DB, csConversation.WorkspaceId.Get()).Save(csConversation)
			if err != nil {
				logger.Debug("Failed to update cs conversation %s: %v", csConversation.ID, err)
				continue
			}
		} else {
			csMessage.CustomerServiceId.Set(csConversation.CustomerServiceId.Get())
			csMessage, err := csMessage.Repo(w.DB, csConversation.WorkspaceId.Get()).Save(csMessage)
			if err != nil {
				logger.Debug("Failed to update message %s: %v", csMessage.ID, err)
				continue
			}
			csConversation.LastMessageId.Set(csMessage.ID)

			// Reset unread count when customer service sends a message
			csConversation.UnreadCount.Set(0)

			csConversation, err = csConversation.Repo(w.DB, csConversation.WorkspaceId.Get()).Save(csConversation)
			if err != nil {
				logger.Debug("Failed to update cs conversation %s: %v", csConversation.ID, err)
				continue
			}
		}

		// Call post webhook handlers
		if csMessage.Message != nil {
			PostInstagramService.CallPostHandlers(payload, csConversation.Conversation, csMessage.Message, csConversation.WorkspaceId.Get())
		}
	}
	return nil
}

func (w *webhookHandler) getCsConversation(msg instagram_api.WebhookMessage, senderID string, receiverID string) (conversation *entities.CsConversation, msgFromCustomer bool, err error) {
	// Find the existing conversation by Instagram IDs
	csConversation, err := new(entities.CsConversation).RepoWithoutWorkspace(
		w.DB.Joins("ChatChannel").Joins("Customer").Preload("Conversation")).FindOne(
		metaorm.And(
			metaorm.Eq("*ChatChannel.business_account_id", receiverID),
			metaorm.Eq("*Customer.instagram_id", senderID),
		),
	)
	if err != nil {
		return nil, false, err
	}
	if csConversation != nil {
		return csConversation, true, nil
	}

	csConversation, err = new(entities.CsConversation).RepoWithoutWorkspace(
		w.DB.Joins("ChatChannel").Joins("Customer")).FindOne(
		metaorm.And(
			metaorm.Eq("*ChatChannel.business_account_id", senderID),
			metaorm.Eq("*Customer.instagram_id", receiverID),
		),
	)
	if err != nil {
		return nil, false, err
	}
	if csConversation != nil {
		return csConversation, false, nil
	}

	// Create new conversation
	customerID := ""
	msgFromCustomer = false
	channel, err := new(entities.ChatChannel).RepoWithoutWorkspace(w.DB).FindOne(w.DB.Eq("*business_account_id", receiverID))
	if err != nil {
		return nil, false, err
	}
	if channel == nil {
		channel, err := new(entities.ChatChannel).RepoWithoutWorkspace(w.DB).FindOne(w.DB.Eq("*business_account_id", senderID))
		if err != nil {
			return nil, false, err
		}
		if channel == nil {
			return nil, false, errors.ErrChatChannelNotFound
		}
		customerID = receiverID
		msgFromCustomer = false
	} else {
		customerID = senderID
		msgFromCustomer = true
	}

	customer, err := new(relationshipEntities.Customer).Repo(w.DB, channel.WorkspaceId.Get()).FindOne(w.DB.Eq("*instagram_id", customerID))
	if err != nil {
		return nil, false, err
	}
	if customer == nil {
		customer = &relationshipEntities.Customer{}
		customer.InstagramId.Set(customerID)
		customer.Active.Set(true)
		customer.DisplayName.Set(customerID) // Use customerID as default display name
		customer, err = customer.Repo(w.DB, channel.WorkspaceId.Get()).Save(customer)
		if err != nil {
			return nil, false, err
		}
	}

	newConversation := &entities.Conversation{}
	newConversation.UUID.Generate()
	newConversation.ChatChannelId.Set(channel.ID)
	newConversation.Type.Set(configs.ConversationTypeDirect)
	newConversation, err = newConversation.Repo(w.DB).Save(newConversation)
	if err != nil {
		return nil, false, err
	}

	newCsConversation := &entities.CsConversation{}
	newCsConversation.ConversationId.Set(newConversation.ID)
	newCsConversation.ChatChannelId.Set(channel.ID)
	newCsConversation.CustomerId.Set(customer.ID)
	newCsConversation.Pinned.Set(false)
	newCsConversation.CustomerLastMessageId.SetNull()
	newCsConversation.UnreadCount.Set(0)
	newCsConversation, err = newCsConversation.Repo(w.DB, channel.WorkspaceId.Get()).Save(newCsConversation)
	if err != nil {
		return nil, false, err
	}
	return newCsConversation, msgFromCustomer, nil
}

func (w *webhookHandler) initMessage(
	msg instagram_api.WebhookMessage,
	channel *entities.ChatChannel,
	csConversation *entities.CsConversation,
	senderId string,
	receiverId string,
) (*entities.CsMessage, error) {
	message := &entities.Message{}
	message.UUID.Generate()
	message.ConversationId.Set(csConversation.Conversation.ID)
	message.PlatformId.Set(msg.MID)
	message.PlatformSenderId.Set(senderId)
	message.PlatformRecipientId.Set(receiverId)
	message.Timestamp.Set(msg.Timestamp / 1000) // Convert milliseconds to seconds
	message.Status.Set(configs.MessageStatusDelivered)

	// Set message content based on type
	if msg.Text != "" {
		message.MessageType.Set(configs.MessageTypeText)
		message.Text.Set(msg.Text)
	} else if len(msg.Attachments) > 0 {
		attachment := msg.Attachments[0]
		message.MessageType.Set(attachment.Type)

		// Download and save media
		mediaContent, err := w.downloadAndSaveMedia(attachment.URL)
		if err != nil {
			logger.Error("Error downloading and saving media:", err)
			return nil, err
		}
		message.MediaContent.Set(mediaContent)
		filename, _ := nanoid.NewSafe()
		message.Filename.Set(filename)
	} else {
		message.MessageType.Set(configs.MessageTypeText)
		message.Text.Set("") // Empty text message
	}

	// Save the message first
	message, err := message.Repo(w.DB, channel.WorkspaceId.Get()).Save(message)
	if err != nil {
		return nil, err
	}

	// Create CsMessage
	csMessage := &entities.CsMessage{}
	csMessage.MessageId.Set(message.ID)
	csMessage.CsConversationId.Set(csConversation.ID)
	csMessage.ChatChannelId.Set(channel.ID)

	return csMessage, nil
}

func (w *webhookHandler) downloadAndSaveMedia(mediaUrl string) (string, error) {
	// Download media from Instagram
	resp, err := http.Get(mediaUrl)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// Read the response body
	mediaBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// Convert to base64 using base64static
	base64Content, err := base64static.FileToBase64(mediaBytes)
	if err != nil {
		logger.Error("Failed to convert media to base64: %v", err)
		// Fallback to just returning the URL
		return mediaUrl, nil
	}

	return base64Content, nil
}
