module github.com/metadiv-tech/mod_chat

go 1.23.2

require (
	github.com/gorilla/websocket v1.5.3
	github.com/metadiv-tech/base64static v1.1.0
	github.com/metadiv-tech/instagram_api v1.0.0
	github.com/metadiv-tech/logger v1.1.0
	github.com/metadiv-tech/metagin v1.5.4
	github.com/metadiv-tech/metaorm v1.2.25
	github.com/metadiv-tech/mod_data v1.1.1
	github.com/metadiv-tech/mod_relationship v1.1.2
	github.com/metadiv-tech/mod_saas v1.4.10
	github.com/metadiv-tech/mod_utils v1.2.11
	github.com/metadiv-tech/mod_workflow v0.0.0-20250625045208-5e3236bbd688
	github.com/metadiv-tech/nanoid v1.1.0
	github.com/metadiv-tech/whatsapp_api v1.1.3
	github.com/metadiv-tech/workflow v0.0.0-20250623073018-8436b455328e
)

require (
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/bradfitz/gomemcache v0.0.0-20250403215159-8d39553ac7cf // indirect
	github.com/brianvoe/gofakeit/v7 v7.2.1 // indirect
	github.com/bytedance/sonic v1.13.3 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/gabriel-vasile/mimetype v1.4.9 // indirect
	github.com/gin-contrib/cache v1.4.0 // indirect
	github.com/gin-contrib/cors v1.7.6 // indirect
	github.com/gin-contrib/sse v1.1.0 // indirect
	github.com/gin-gonic/gin v1.10.1 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.26.0 // indirect
	github.com/go-sql-driver/mysql v1.9.3 // indirect
	github.com/goccy/go-json v0.10.5 // indirect
	github.com/golang-jwt/jwt/v5 v5.2.2 // indirect
	github.com/gomodule/redigo v1.9.2 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20240606120523-5a60cdf6a761 // indirect
	github.com/jackc/pgx/v5 v5.7.5 // indirect
	github.com/jackc/puddle/v2 v2.2.2 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/cpuid/v2 v2.2.10 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/matoous/go-nanoid v1.5.1 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-sqlite3 v1.14.28 // indirect
	github.com/memcachier/mc/v3 v3.0.3 // indirect
	github.com/metadiv-tech/aes v1.2.3 // indirect
	github.com/metadiv-tech/env v1.1.0 // indirect
	github.com/metadiv-tech/http_call v1.2.0 // indirect
	github.com/metadiv-tech/jwt v1.1.1 // indirect
	github.com/metadiv-tech/mime v1.1.1 // indirect
	github.com/metadiv-tech/pwd v1.1.0 // indirect
	github.com/metadiv-tech/rsa v1.1.0 // indirect
	github.com/metadiv-tech/smtp_dialer v1.1.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/pelletier/go-toml/v2 v2.2.4 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/robfig/cron v1.2.0 // indirect
	github.com/robfig/go-cache v0.0.0-20130306151617-9fc39e0dbf62 // indirect
	github.com/tkrajina/go-reflector v0.5.8 // indirect
	github.com/tkrajina/typescriptify-golang-structs v0.2.0 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.3.0 // indirect
	github.com/ulule/limiter/v3 v3.11.2 // indirect
	golang.org/x/arch v0.18.0 // indirect
	golang.org/x/crypto v0.39.0 // indirect
	golang.org/x/net v0.41.0 // indirect
	golang.org/x/sync v0.15.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.26.0 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/alexcesaro/quotedprintable.v3 v3.0.0-20150716171945-2caba252f4dc // indirect
	gopkg.in/mail.v2 v2.3.1 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/mysql v1.6.0 // indirect
	gorm.io/driver/postgres v1.6.0 // indirect
	gorm.io/driver/sqlite v1.6.0 // indirect
	gorm.io/gorm v1.30.0 // indirect
)
