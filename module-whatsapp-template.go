package mod_chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/endpoints/whatsapp_template"
	"github.com/metadiv-tech/mod_chat/entities"
)

var ModuleWhatsappTemplate = metagin.NewModule("whatsapp-template", "v1", func(m *metagin.Module) {
	m.RegisterMigration(
		&entities.WhatsappTemplate{},
		&entities.WhatsappTemplateContent{},
	)
	m.<PERSON><PERSON>and<PERSON>(
		whatsapp_template.ApiWhatsappTemplateCategoryGet,
		whatsapp_template.ApiWhatsappTemplateCategoryList,
		whatsapp_template.ApiWhatsappTemplateDelete,
		whatsapp_template.ApiWhatsappTemplateGet,
		whatsapp_template.ApiWhatsappTemplateLanguageGet,
		whatsapp_template.ApiWhatsappTemplateLanguageList,
		whatsapp_template.ApiWhatsappTemplateList,
		whatsapp_template.ApiWhatsappTemplateSave,
		whatsapp_template.ApiWhatsappTemplateUpdate,
		whatsapp_template.CronWhatsappTemplateUpdatePending,
	)
})
