package entities

import (
	"github.com/metadiv-tech/metaorm/base"
	"github.com/metadiv-tech/metaorm/types"
	customerEntities "github.com/metadiv-tech/mod_relationship/entities"
	saasEntities "github.com/metadiv-tech/mod_saas/entities"
)

type CsMessage struct {
	base.ModelWorkspace[CsMessage]

	MessageId types.UInt `json:"message_id"`
	Message   *Message   `json:"message" gorm:"foreignKey:MessageId"`

	ReplyToMessageId types.UInt `json:"reply_to_message_id"`
	ReplyToMessage   *CsMessage `json:"reply_to_message" gorm:"foreignKey:ReplyToMessageId"`

	CsConversationId types.UInt      `json:"cs_conversation_id"`
	CsConversation   *CsConversation `json:"cs_conversation" gorm:"foreignKey:CsConversationId"`

	ChatChannelId types.UInt   `json:"chat_channel_id"`
	ChatChannel   *ChatChannel `json:"chat_channel" gorm:"foreignKey:ChatChannelId"`

	CustomerId types.UInt                 `json:"customer_id"`
	Customer   *customerEntities.Customer `json:"customer" gorm:"foreignKey:CustomerId"`

	CustomerServiceId types.UInt                  `json:"customer_service_id"`
	CustomerService   *saasEntities.WorkspaceUser `json:"customer_service" gorm:"foreignKey:CustomerServiceId"`
}

func (m *CsMessage) TableName() string {
	return "cs_messages"
}

type CsMessageDTO struct {
	base.ModelWorkspaceDTO

	Message         *MessageDTO                    `json:"message"`
	ReplyToMessage  *CsMessageDTO                  `json:"reply_to_message"`
	CsConversation  *CsConversationDTO             `json:"cs_conversation"`
	ChatChannel     *ChatChannelDTO                `json:"chat_channel"`
	Customer        *customerEntities.CustomerDTO  `json:"customer"`
	CustomerService *saasEntities.WorkspaceUserDTO `json:"customer_service"`
}

func (m *CsMessage) DTO() *CsMessageDTO {
	d := &CsMessageDTO{
		ModelWorkspaceDTO: *m.ModelWorkspace.DTO(),
	}
	if m.ReplyToMessage != nil {
		d.ReplyToMessage = m.ReplyToMessage.DTO()
	}
	if m.CsConversation != nil {
		d.CsConversation = m.CsConversation.DTO()
	}
	if m.ChatChannel != nil {
		d.ChatChannel = m.ChatChannel.DTO()
	}
	if m.Customer != nil {
		d.Customer = m.Customer.DTO()
	}
	if m.CustomerService != nil {
		d.CustomerService = m.CustomerService.DTO()
	}
	if m.Message != nil {
		d.Message = m.Message.DTO()
	}
	return d
}
