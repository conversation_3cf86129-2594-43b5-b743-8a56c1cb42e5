package entities

import (
	"github.com/metadiv-tech/metaorm/base"
	"github.com/metadiv-tech/metaorm/types"
	customerEntities "github.com/metadiv-tech/mod_relationship/entities"
	saasEntities "github.com/metadiv-tech/mod_saas/entities"
)

type CsConversation struct {
	base.ModelWorkspace[CsConversation]

	ConversationId types.UInt    `json:"conversation_id"`
	Conversation   *Conversation `json:"conversation" gorm:"foreignKey:ConversationId"`

	ChatChannelId types.UInt   `json:"chat_channel_id"`
	ChatChannel   *ChatChannel `json:"chat_channel" gorm:"foreignKey:ChatChannelId"`

	LastMessageId types.UInt `json:"last_message_id"`
	LastMessage   *CsMessage `json:"last_message" gorm:"foreignKey:LastMessageId"`

	CustomerId types.UInt                 `json:"customer_id"`
	Customer   *customerEntities.Customer `json:"customer" gorm:"foreignKey:CustomerId"`

	CustomerServiceId types.UInt                  `json:"customer_service_id"`
	CustomerService   *saasEntities.WorkspaceUser `json:"customer_service" gorm:"foreignKey:CustomerServiceId"`

	CustomerLastMessageId types.UInt `json:"customer_last_message_id"`
	CustomerLastMessage   *CsMessage `json:"customer_last_message" gorm:"foreignKey:CustomerLastMessageId"`

	UnreadCount types.UInt      `json:"unread_count"`
	Pinned      types.Boolean   `json:"pinned"`
	ClosedAt    types.Timestamp `json:"closed_at"`
}

func (c *CsConversation) TableName() string {
	return "cs_conversations"
}

func (c *CsConversation) DTO() *CsConversationDTO {
	d := &CsConversationDTO{
		ModelWorkspaceDTO: *c.ModelWorkspace.DTO(),
		UnreadCount:       c.UnreadCount.Get(),
		Pinned:            c.Pinned.Get(),
		ClosedAt:          c.ClosedAt.Get(),
	}
	if c.Conversation != nil {
		d.Conversation = c.Conversation.DTO()
	}
	if c.ChatChannel != nil {
		d.ChatChannel = c.ChatChannel.DTO()
	}
	if c.Customer != nil {
		d.Customer = c.Customer.DTO()
	}
	if c.CustomerService != nil {
		d.CustomerService = c.CustomerService.DTO()
	}
	if c.CustomerLastMessage != nil {
		d.CustomerLastMessage = c.CustomerLastMessage.DTO()
	}
	if c.LastMessage != nil {
		d.LastMessage = c.LastMessage.DTO()
	}
	return d
}

type CsConversationDTO struct {
	base.ModelWorkspaceDTO

	Conversation        *ConversationDTO               `json:"conversation"`
	ChatChannel         *ChatChannelDTO                `json:"chat_channel"`
	Customer            *customerEntities.CustomerDTO  `json:"customer"`
	CustomerService     *saasEntities.WorkspaceUserDTO `json:"customer_service"`
	CustomerLastMessage *CsMessageDTO                  `json:"customer_last_message"`
	LastMessage         *CsMessageDTO                  `json:"last_message"`
	UnreadCount         uint                           `json:"unread_count"`
	Pinned              bool                           `json:"pinned"`
	ClosedAt            int64                          `json:"closed_at"`
}
