package entities

import (
	"github.com/metadiv-tech/metaorm/base"
	"github.com/metadiv-tech/metaorm/types"
)

type WhatsappTemplate struct {
	base.ModelWorkspace[WhatsappTemplate]

	ChannelId types.UInt   `json:"channel_id"`
	Channel   *ChatChannel `json:"channel" gorm:"foreignKey:ChannelId"`

	Name     types.EncryptedText       `json:"name"`
	Category types.Varchar             `json:"category"`
	Contents []WhatsappTemplateContent `json:"contents" gorm:"foreignKey:TemplateId"`
}

type WhatsappTemplateDTO struct {
	base.ModelWorkspaceDTO

	Channel *ChatChannelDTO `json:"channel"`

	Name     string                       `json:"name"`
	Category string                       `json:"category"`
	Contents []WhatsappTemplateContentDTO `json:"contents"`
}

func (c *WhatsappTemplate) TableName() string {
	return "cs_whatsapp_templates"
}

func (c *WhatsappTemplate) DTO() *WhatsappTemplateDTO {
	d := &WhatsappTemplateDTO{
		ModelWorkspaceDTO: *c.ModelWorkspace.DTO(),
		Name:              c.Name.Get(),
		Category:          c.Category.Get(),
		Contents:          make([]WhatsappTemplateContentDTO, len(c.Contents)),
	}
	if c.Channel != nil {
		d.Channel = c.Channel.DTO()
	}
	for i, content := range c.Contents {
		d.Contents[i] = *content.DTO()
	}

	return d
}
