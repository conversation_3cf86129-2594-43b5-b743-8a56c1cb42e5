package entities

import (
	"github.com/metadiv-tech/metaorm/base"
	"github.com/metadiv-tech/metaorm/types"
)

type MessageLocation struct {
	LocLatitude  float64 `json:"loc_latitude"`
	LocLongitude float64 `json:"loc_longitude"`
	LocName      string  `json:"loc_name"`
	LocAddress   string  `json:"loc_address"`
}

type Message struct {
	base.ModelWorkspace[Message]

	UUID                types.UUID      `json:"uuid"`
	MessageType         types.Varchar   `json:"message_type"`
	Timestamp           types.Timestamp `json:"timestamp"`
	PlatformId          types.Varchar   `json:"platform_id"`
	PlatformSenderId    types.Varchar   `json:"platform_sender_id"`
	PlatformRecipientId types.Varchar   `json:"platform_recipient_id"`
	Status              types.Varchar   `json:"status"`
	Retry               types.Int       `json:"retry"`
	RetryAt             types.Timestamp `json:"retry_at"`

	ConversationId types.UInt    `json:"conversation_id"`
	Conversation   *Conversation `json:"conversation" gorm:"foreignKey:ConversationId"`

	ReplyToMessageId types.UInt `json:"reply_to_message_id"`
	ReplyToMessage   *Message   `json:"reply_to_message" gorm:"foreignKey:ReplyToMessageId"`

	Text         types.Varchar               `json:"text"`
	Filename     types.Varchar               `json:"filename"`
	Emojis       types.StringSlice           `json:"emojis"`
	MediaContent types.EncryptedText         `json:"media_content"`
	Location     types.Json[MessageLocation] `json:"location"`

	TemplateName     types.Varchar       `json:"template_name"`
	TemplateLanguage types.Varchar       `json:"template_language"`
	TemplateHeader   types.EncryptedText `json:"template_header"`
	TemplateBody     types.EncryptedText `json:"template_body"`
	TemplateFooter   types.EncryptedText `json:"template_footer"`

	TemplateHeaderParams types.Json[[]WhatsappTemplateParam] `json:"template_header_params"`
	TemplateBodyParams   types.Json[[]WhatsappTemplateParam] `json:"template_body_params"`
	TemplateFooterParams types.Json[[]WhatsappTemplateParam] `json:"template_footer_params"`
}

func (m *Message) TableName() string {
	return "messages"
}

func (m *Message) DTO() *MessageDTO {
	d := &MessageDTO{
		ModelWorkspaceDTO:   *m.ModelWorkspace.DTO(),
		UUID:                m.UUID.Get(),
		MessageType:         m.MessageType.Get(),
		Timestamp:           m.Timestamp.Get(),
		PlatformId:          m.PlatformId.Get(),
		PlatformSenderId:    m.PlatformSenderId.Get(),
		PlatformRecipientId: m.PlatformRecipientId.Get(),
		Text:                m.Text.Get(),
		Filename:            m.Filename.Get(),
		Emojis:              m.Emojis.Get(),
		MediaContent:        m.MediaContent.Get(),
		Location:            m.Location.Get(),
		Status:              m.Status.Get(),
		Retry:               m.Retry.Get(),
		RetryAt:             m.RetryAt.Get(),
		TemplateName:        m.TemplateName.Get(),
		TemplateLanguage:    m.TemplateLanguage.Get(),
		TemplateHeader:      m.TemplateHeader.Get(),
		TemplateBody:        m.TemplateBody.Get(),
		TemplateFooter:      m.TemplateFooter.Get(),
	}
	if m.Conversation != nil {
		d.Conversation = m.Conversation.DTO()
	}
	if m.ReplyToMessage != nil {
		d.ReplyToMessage = m.ReplyToMessage.DTO()
	}
	if m.TemplateHeaderParams.Get() != nil {
		d.TemplateHeaderParams = *m.TemplateHeaderParams.Get()
	}
	if m.TemplateBodyParams.Get() != nil {
		d.TemplateBodyParams = *m.TemplateBodyParams.Get()
	}
	if m.TemplateFooterParams.Get() != nil {
		d.TemplateFooterParams = *m.TemplateFooterParams.Get()
	}
	return d
}

type MessageDTO struct {
	base.ModelWorkspaceDTO

	UUID                string `json:"uuid"`
	MessageType         string `json:"message_type"`
	Timestamp           int64  `json:"timestamp"`
	PlatformId          string `json:"platform_id"`
	PlatformSenderId    string `json:"platform_sender_id"`
	PlatformRecipientId string `json:"platform_recipient_id"`

	Conversation             *ConversationDTO `json:"conversation"`
	ReplyToMessage           *MessageDTO      `json:"reply_to"`
	PlatformReplyToMessageId string           `json:"platform_reply_to_message_id"`

	Text         string           `json:"text"`
	Filename     string           `json:"filename"`
	Emojis       []string         `json:"emojis"`
	MediaContent string           `json:"media_content"`
	Location     *MessageLocation `json:"location"`

	Status  string `json:"status"`
	Retry   int    `json:"retry"`
	RetryAt int64  `json:"retry_at"`

	TemplateName     string `json:"template_name"`
	TemplateLanguage string `json:"template_language"`
	TemplateHeader   string `json:"template_header"`
	TemplateBody     string `json:"template_body"`
	TemplateFooter   string `json:"template_footer"`

	TemplateHeaderParams []WhatsappTemplateParam `json:"template_header_params"`
	TemplateBodyParams   []WhatsappTemplateParam `json:"template_body_params"`
	TemplateFooterParams []WhatsappTemplateParam `json:"template_footer_params"`
}
