package entities

import (
	"github.com/metadiv-tech/instagram_api"
	"github.com/metadiv-tech/metaorm/base"
	"github.com/metadiv-tech/metaorm/types"
	mod_saas "github.com/metadiv-tech/mod_saas/entities"
	"github.com/metadiv-tech/whatsapp_api"
)

type ChatChannel struct {
	base.ModelWorkspace[ChatChannel]

	Platform    types.Varchar       `json:"platform"`
	AccessToken types.EncryptedText `json:"access_token"`
	DisplayName types.EncryptedText `json:"display_name"`
	PhoneNumber types.EncryptedText `json:"phone_number"`

	// WhatsApp specific fields
	BusinessAccountID  types.Varchar       `json:"business_account_id"`
	PhoneNumberID      types.EncryptedText `json:"phone_number_id"`
	DisplayPhoneNumber types.EncryptedText `json:"display_phone_number"`

	Secret   types.UUID    `json:"secret"` // for whatsapp webhook
	IsActive types.Boolean `json:"is_active"`

	AllowAllUsers types.Boolean            `json:"allow_all_users"`
	Users         []mod_saas.WorkspaceUser `json:"users" gorm:"many2many:chat_channel_users;"`
}

func (c *ChatChannel) TableName() string {
	return "chat_channels"
}

func (c *ChatChannel) DTO() *ChatChannelDTO {
	d := &ChatChannelDTO{
		ModelWorkspaceDTO:  *c.ModelWorkspace.DTO(),
		Platform:           c.Platform.Get(),
		AccessToken:        c.AccessToken.Get(),
		DisplayName:        c.DisplayName.Get(),
		PhoneNumber:        c.PhoneNumber.Get(),
		BusinessAccountID:  c.BusinessAccountID.Get(),
		PhoneNumberID:      c.PhoneNumberID.Get(),
		DisplayPhoneNumber: c.DisplayPhoneNumber.Get(),
		Secret:             c.Secret.Get(),
		IsActive:           c.IsActive.Get(),
	}
	if len(c.Users) > 0 {
		d.Users = make([]mod_saas.WorkspaceUserDTO, len(c.Users))
		for i, user := range c.Users {
			d.Users[i] = *user.DTO()
		}
	}
	return d
}

type ChatChannelDTO struct {
	base.ModelWorkspaceDTO
	Platform           string `json:"platform"`
	AccessToken        string `json:"access_token"`
	DisplayName        string `json:"display_name"`
	PhoneNumber        string `json:"phone_number"`
	BusinessAccountID  string `json:"business_account_id"`
	PhoneNumberID      string `json:"phone_number_id"`
	DisplayPhoneNumber string `json:"display_phone_number"`
	Secret             string `json:"secret"`
	IsActive           bool   `json:"is_active"`

	Users []mod_saas.WorkspaceUserDTO `json:"users"`
}

func (c *ChatChannel) WhatsappClient() (*whatsapp_api.Client, error) {
	return whatsapp_api.NewClient(c.BusinessAccountID.Get(), c.AccessToken.Get(), c.PhoneNumberID.Get())
}

func (c *ChatChannel) InstagramClient() (*instagram_api.Client, error) {
	return instagram_api.NewClient(c.BusinessAccountID.Get(), c.AccessToken.Get()), nil
}
