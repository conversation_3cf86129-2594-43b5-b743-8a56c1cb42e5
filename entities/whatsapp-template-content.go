package entities

import (
	"github.com/metadiv-tech/metaorm/base"
	"github.com/metadiv-tech/metaorm/types"
)

type WhatsappTemplateContent struct {
	base.ModelWorkspace[WhatsappTemplateContent]

	TemplateId types.UInt        `json:"template_id"`
	Template   *WhatsappTemplate `json:"template" gorm:"foreignKey:TemplateId"`

	WaID     types.Varchar `json:"wa_id"`
	Language types.Varchar `json:"language"`

	HeaderType      types.Varchar                       `json:"header_type"`
	HeaderMediaFile types.Varchar                       `json:"header_media_file"`
	HeaderContent   types.EncryptedText                 `json:"header_content"`
	HeaderParams    types.Json[[]WhatsappTemplateParam] `json:"header_params"`

	BodyContent types.EncryptedText                 `json:"body_content"`
	BodyParams  types.Json[[]WhatsappTemplateParam] `json:"body_params"`

	FooterContent types.EncryptedText `json:"footer_content"`

	Status types.Varchar `json:"status"`
}

type WhatsappTemplateParam struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type WhatsappTemplateContentDTO struct {
	base.ModelWorkspaceDTO

	Template *WhatsappTemplateDTO `json:"template"`

	WaID     string `json:"wa_id"`
	Language string `json:"language"`

	HeaderType      string                   `json:"header_type"`
	HeaderMediaFile string                   `json:"header_media_file"`
	HeaderContent   string                   `json:"header_content"`
	HeaderParams    *[]WhatsappTemplateParam `json:"header_params"`

	BodyContent string                   `json:"body_content"`
	BodyParams  *[]WhatsappTemplateParam `json:"body_params"`

	FooterContent string `json:"footer_content"`

	Status string `json:"status"`
}

func (c *WhatsappTemplateContent) TableName() string {
	return "cs_whatsapp_template_contents"
}

func (c *WhatsappTemplateContent) DTO() *WhatsappTemplateContentDTO {
	d := &WhatsappTemplateContentDTO{
		ModelWorkspaceDTO: *c.ModelWorkspace.DTO(),
		WaID:              c.WaID.Get(),
		Language:          c.Language.Get(),
		HeaderType:        c.HeaderType.Get(),
		HeaderMediaFile:   c.HeaderMediaFile.Get(),
		HeaderContent:     c.HeaderContent.Get(),
		HeaderParams:      c.HeaderParams.Get(),
		BodyContent:       c.BodyContent.Get(),
		BodyParams:        c.BodyParams.Get(),
		FooterContent:     c.FooterContent.Get(),
		Status:            c.Status.Get(),
	}
	if c.Template != nil {
		d.Template = c.Template.DTO()
	}
	return d
}
