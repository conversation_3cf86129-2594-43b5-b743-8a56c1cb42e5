package entities

import (
	"github.com/metadiv-tech/metaorm/base"
	"github.com/metadiv-tech/metaorm/types"
	relationshipEntities "github.com/metadiv-tech/mod_relationship/entities"
)

type WhatsappBroadcastJob struct {
	base.ModelWorkspace[WhatsappBroadcastJob]

	BroadcastId types.UInt         `json:"broadcast_id"`
	Broadcast   *WhatsappBroadcast `json:"broadcast" gorm:"foreignKey:BroadcastId"`

	CustomerId types.UInt                     `json:"customer_id"`
	Customer   *relationshipEntities.Customer `json:"customer"`

	Result types.LongText `json:"result"`
	Status types.Varchar  `json:"status"` // pending, sending, sent, failed
}

type WhatsappBroadcastJobDTO struct {
	base.ModelWorkspaceDTO

	Customer *relationshipEntities.CustomerDTO `json:"customer"`

	Result string `json:"result"`
	Status string `json:"status"`
}

func (c *WhatsappBroadcastJob) TableName() string {
	return "cs_whatsapp_broadcast_jobs"
}

func (c *WhatsappBroadcastJob) DTO() *WhatsappBroadcastJobDTO {
	d := &WhatsappBroadcastJobDTO{
		ModelWorkspaceDTO: *c.ModelWorkspace.DTO(),
		Result:            c.Result.Get(),
		Status:            c.Status.Get(),
	}
	if c.Customer != nil {
		d.Customer = c.Customer.DTO()
	}
	return d
}
