package entities

import (
	"github.com/metadiv-tech/metaorm/base"
	"github.com/metadiv-tech/metaorm/types"
)

type Conversation struct {
	base.Model[Conversation]

	UUID types.UUID    `json:"uuid"`
	Type types.Varchar `json:"type"`

	ChatChannelId types.UInt   `json:"chat_channel_id"`
	ChatChannel   *ChatChannel `json:"chat_channel" metaorm:"relation:ChatChannelId"`

	Name types.Varchar `json:"name"` // for group or channel

	LastMessageId types.UInt `json:"last_message_id"`
	LastMessage   *Message   `json:"last_message" metaorm:"relation:LastMessageId"`

	ClosedAt types.Timestamp `json:"closed_at"`
}

func (c *Conversation) TableName() string {
	return "conversations"
}

func (c *Conversation) DTO() *ConversationDTO {
	d := &ConversationDTO{
		ModelDTO: *c.Model.DTO(),
		UUID:     c.UUID.Get(),
		Type:     c.Type.Get(),
		Name:     c.Name.Get(),
		ClosedAt: c.ClosedAt.Get(),
	}
	if c.ChatChannel != nil {
		d.ChatChannel = c.ChatChannel.DTO()
	}
	if c.LastMessage != nil {
		d.LastMessage = c.LastMessage.DTO()
	}
	return d
}

type ConversationDTO struct {
	base.ModelDTO

	UUID     string `json:"uuid"`
	Type     string `json:"type"`
	Name     string `json:"name"` // for group or channel
	ClosedAt int64  `json:"closed_at"`

	LastMessage *MessageDTO     `json:"last_message"`
	ChatChannel *ChatChannelDTO `json:"chat_channel"`
}
