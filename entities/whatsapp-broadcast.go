package entities

import (
	"github.com/metadiv-tech/metaorm/base"
	"github.com/metadiv-tech/metaorm/types"
	workflowEntities "github.com/metadiv-tech/mod_workflow/entities"
)

type WhatsappBroadcast struct {
	base.ModelWorkspace[WhatsappBroadcast]

	Name types.Varchar `json:"name"`

	ChannelId types.UInt   `json:"channel_id"`
	Channel   *ChatChannel `json:"channel" gorm:"foreignKey:ChannelId"`

	TemplateId types.UInt        `json:"template_id"`
	Template   *WhatsappTemplate `json:"template" gorm:"foreignKey:TemplateId"`

	TemplateLang types.Varchar                       `json:"template_lang"`
	HeaderParams types.Json[[]WhatsappTemplateParam] `json:"header_params"`
	BodyParams   types.Json[[]WhatsappTemplateParam] `json:"body_params"`
	FooterParams types.Json[[]WhatsappTemplateParam] `json:"footer_params"`

	SenderId types.UInt `json:"sender_id"` // workspace user id

	Jobs []WhatsappBroadcastJob `json:"jobs" gorm:"foreignKey:BroadcastId"`

	WorkflowId types.UInt                 `json:"workflow_id"`
	Workflow   *workflowEntities.Workflow `json:"workflow" gorm:"foreignKey:WorkflowId"`

	StartAfter types.Timestamp `json:"start_after"`
	Status     types.Varchar   `json:"status"`
}

type WhatsappBroadcastDTO struct {
	base.ModelWorkspaceDTO

	Name string `json:"name"`

	Channel  *ChatChannelDTO      `json:"channel"`
	Template *WhatsappTemplateDTO `json:"template"`

	TemplateLang string `json:"template_lang"`

	HeaderParams []WhatsappTemplateParam `json:"header_params"`
	BodyParams   []WhatsappTemplateParam `json:"body_params"`
	FooterParams []WhatsappTemplateParam `json:"footer_params"`

	Jobs     []WhatsappBroadcastJobDTO     `json:"jobs" gorm:"foreignKey:BroadcastId"`
	Workflow *workflowEntities.WorkflowDTO `json:"workflow"`

	StartAfter int64  `json:"start_after"`
	Status     string `json:"status"`
}

func (c *WhatsappBroadcast) TableName() string {
	return "cs_whatsapp_broadcasts"
}

func (c *WhatsappBroadcast) DTO() *WhatsappBroadcastDTO {
	d := &WhatsappBroadcastDTO{
		ModelWorkspaceDTO: *c.ModelWorkspace.DTO(),
		Name:              c.Name.Get(),
		TemplateLang:      c.TemplateLang.Get(),
		StartAfter:        c.StartAfter.Get(),
		Status:            c.Status.Get(),
	}
	if c.Channel != nil {
		d.Channel = c.Channel.DTO()
	}
	if c.Template != nil {
		d.Template = c.Template.DTO()
	}
	if c.HeaderParams.Get() != nil {
		d.HeaderParams = *c.HeaderParams.Get()
	}
	if c.BodyParams.Get() != nil {
		d.BodyParams = *c.BodyParams.Get()
	}
	if c.FooterParams.Get() != nil {
		d.FooterParams = *c.FooterParams.Get()
	}
	if c.Jobs != nil {
		d.Jobs = make([]WhatsappBroadcastJobDTO, len(c.Jobs))
		for i, job := range c.Jobs {
			d.Jobs[i] = *job.DTO()
		}
	}
	if c.Workflow != nil {
		d.Workflow = c.Workflow.DTO()
	}
	return d
}
