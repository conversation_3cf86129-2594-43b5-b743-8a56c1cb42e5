package entities

import (
	"github.com/metadiv-tech/metaorm/base"
	"github.com/metadiv-tech/metaorm/types"
	mod_saas "github.com/metadiv-tech/mod_saas/entities"
)

type ChatChannelUser struct {
	base.Repo[ChatChannelUser]

	ChatChannelId types.UInt   `json:"chat_channel_id"`
	ChatChannel   *ChatChannel `json:"chat_channel" gorm:"foreignKey:ChatChannelId"`

	WorkspaceUserId types.UInt              `json:"workspace_user_id"`
	WorkspaceUser   *mod_saas.WorkspaceUser `json:"workspace_user" gorm:"foreignKey:WorkspaceUserId"`
}

func (c *ChatChannelUser) TableName() string {
	return "chat_channel_users"
}
