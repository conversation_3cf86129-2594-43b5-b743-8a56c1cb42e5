package chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/chat_service"
)

type ChatChannelListRequest struct {
	base.RequestListing
	Platform    *string `form:"platform,omitempty"`
	IsActive    *string `form:"is_active,omitempty"`
	OnlyAllowed bool    `form:"only_allowed,omitempty"`
}

var ApiChatChannelList = metagin.Get[ChatChannelListRequest, []entities.ChatChannelDTO]("listChatChannel").
	Route("/channel").
	Handler(func(ctx metagin.IContext[ChatChannelListRequest, []entities.ChatChannelDTO]) {
		// Get request data
		req := ctx.Request()

		// Create service manager
		manager := chat_service.NewChannelManager(ctx.DB())

		// List chat channels
		result, pagination, err := manager.ListChannels(
			ctx.WorkspaceId(),
			ctx.WorkspaceUserId(),
			req.Platform,
			req.IsActive,
			req.OnlyAllowed,
			&req.Sorting,
			&req.Pagination,
		)
		if err != nil {
			ctx.Error(err)
			return
		}

		// Return response with pagination
		ctx.OK(&result, pagination)
	}).Build()
