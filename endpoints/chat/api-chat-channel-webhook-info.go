package chat

import (
	"fmt"

	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
	saasSettings "github.com/metadiv-tech/mod_saas/system_settings"
	systemSettingService "github.com/metadiv-tech/mod_utils/services/system_setting_service"
)

type ChatChannelWebhookInfoDTO struct {
	WebhookUrl  string `json:"webhook_url"`
	VerifyToken string `json:"verify_token"`
}

var ApiChatChannelWebhookInfoGet = metagin.Get[base.RequestPathId, ChatChannelWebhookInfoDTO]("getChatChannelWebhookInfo").
	Route("/channel/:id/webhook-info").
	Handler(func(ctx metagin.IContext[base.RequestPathId, ChatChannelWebhookInfoDTO]) {

		adminQuery := systemSettingService.NewAdminQuery(ctx.DB())
		backendUrl := adminQuery.GetByKey(saasSettings.KeySystemBackendUrl).String()

		channel, err := new(entities.ChatChannel).RepoWithoutWorkspace(ctx.DB()).FindOne(ctx.DB().Eq("id", ctx.Request().ID))
		if err != nil {
			ctx.Error(err)
			return
		}
		if channel == nil {
			ctx.Error(errors.ErrChatChannelNotFound)
			return
		}

		ctx.OK(&ChatChannelWebhookInfoDTO{
			WebhookUrl:  fmt.Sprintf("%s/v1/public/cs/whatsapp/webhook", backendUrl),
			VerifyToken: channel.Secret.Get(),
		})
	}).Build()
