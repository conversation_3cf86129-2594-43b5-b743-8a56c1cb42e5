package chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/chat_service"
)

var ApiChatChannelCreate = metagin.Post[chat_service.CreateChannelRequest, entities.ChatChannelDTO]("createChatChannel").
	Route("/channel").
	Handler(func(ctx metagin.IContext[chat_service.CreateChannelRequest, entities.ChatChannelDTO]) {
		// Get request data
		req := ctx.Request()

		// Create service manager
		manager := chat_service.NewChannelManager(ctx.DB())

		// Create chat channel
		result, err := manager.CreateChannel(req, ctx.WorkspaceId())
		if err != nil {
			ctx.Error(err)
			return
		}

		// Return response
		ctx.OK(result)
	}).Build()
