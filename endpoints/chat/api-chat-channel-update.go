package chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/chat_service"
)

type UpdateChannelRequestWithId struct {
	base.RequestPathId
	chat_service.UpdateChannelRequest
}

var ApiChatChannelUpdate = metagin.Put[UpdateChannelRequestWithId, entities.ChatChannelDTO]("updateChatChannel").
	Route("/channel/:id").
	Handler(func(ctx metagin.IContext[UpdateChannelRequestWithId, entities.ChatChannelDTO]) {
		// Get request data
		req := ctx.Request()
		channelId := req.ID

		// Create service manager
		manager := chat_service.NewChannelManager(ctx.DB())

		// Update chat channel
		result, err := manager.UpdateChannel(channelId, &req.UpdateChannelRequest, ctx.WorkspaceId())
		if err != nil {
			ctx.Error(err)
			return
		}

		// Return response
		ctx.OK(result)
	}).Build()
