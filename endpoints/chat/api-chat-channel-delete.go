package chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/services/chat_service"
)

var ApiChatChannelDelete = metagin.Delete[base.RequestPathId, base.Empty]("deleteChatChannel").
	Route("/channel/:id").
	Handler(func(ctx metagin.IContext[base.RequestPathId, base.Empty]) {
		// Get request data
		req := ctx.Request()
		channelId := req.ID

		// Create service manager
		manager := chat_service.NewChannelManager(ctx.DB())

		// Delete chat channel
		err := manager.DeleteChannel(channelId, ctx.WorkspaceId())
		if err != nil {
			ctx.Error(err)
			return
		}

		// Return success response
		ctx.OK(nil)
	}).Build()
