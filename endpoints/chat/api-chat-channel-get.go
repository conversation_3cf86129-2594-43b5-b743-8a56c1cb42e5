package chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/chat_service"
)

var ApiChatChannelGet = metagin.Get[base.RequestPathId, entities.ChatChannelDTO]("getChatChannel").
	Route("/channel/:id").
	Handler(func(ctx metagin.IContext[base.RequestPathId, entities.ChatChannelDTO]) {
		// Get request data
		req := ctx.Request()
		channelId := req.ID

		// Create service manager
		manager := chat_service.NewChannelManager(ctx.DB())

		// Get chat channel
		result, err := manager.GetChannel(channelId, ctx.WorkspaceId())
		if err != nil {
			ctx.Error(err)
			return
		}

		// Return response
		ctx.OK(result)
	}).Build()
