package chat

import (
	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/services/cs_chat_service"
	"github.com/metadiv-tech/mod_chat/services/whatsapp_service"
	"github.com/metadiv-tech/whatsapp_api"
)

var ApiWhatsappWebhook = metagin.Post[whatsapp_api.WebhookPayload, whatsapp_api.WebhookEventResult]("csWhatsappWebhook").
	PublicRoute("/cs/whatsapp/webhook").
	Handler(func(ctx metagin.IPublicContext[whatsapp_api.WebhookPayload, whatsapp_api.WebhookEventResult]) {

		logger.Debug("Whatsapp webhook received:\n", ctx.RawRequest())

		payload := ctx.Request()

		// Register CS post webhook handler
		cs_chat_service.RegisterCsPostWebhookHandler(ctx.DB())

		// Process the webhook payload using whatsapp_api
		apiWebhookHandler := whatsapp_api.NewWebhookHandler()
		result := apiWebhookHandler.ProcessWebhook(payload)

		// Create webhook handler service
		serviceWebhookHandler := whatsapp_service.NewWebhookHandler(ctx.DB())

		// Handle different event types using service layer
		if result.HasMessages() {
			serviceWebhookHandler.HandleIncomingMessages(result.Messages)
		}

		if result.HasMessageStatuses() {
			serviceWebhookHandler.HandleMessageStatuses(result.MessageStatuses)
		}

		if result.HasTemplateStatuses() {
			serviceWebhookHandler.HandleTemplateStatuses(result.TemplateStatuses)
		}

		if result.HasErrors() {
			serviceWebhookHandler.HandleWebhookErrors(result.Errors)
		}

		ctx.OK(result)
	}).Build()
