package cs_chat

import (
	"fmt"

	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/cs_chat_service"
)

// CsConversationListRequest is defined in ws-cs-conversation-list.go

var ApiCsConversationList = metagin.Get[CsConversationListRequest, []entities.CsConversationDTO]("listCsConversation").
	Route("/cs/conversation").
	Handler(func(ctx metagin.IContext[CsConversationListRequest, []entities.CsConversationDTO]) {
		// Get request data
		req := ctx.Request()

		// Create service manager
		manager := cs_chat_service.NewCsConversationManager(ctx.DB())

		// Validate customer_service_id belongs to workspace if provided
		if req.CustomerServiceId != nil {
			valid, err := manager.ValidateCustomerServiceBelongsToWorkspace(ctx.WorkspaceId(), *req.CustomerServiceId)
			if err != nil {
				ctx.Error(err)
				return
			}
			if !valid {
				ctx.Error(fmt.Errorf("customer service does not belong to workspace"))
				return
			}
		}

		// List CS conversations
		result, pagination, err := manager.ListCsConversations(
			ctx.WorkspaceId(),
			ctx.WorkspaceUserId(),
			ctx.IsAdmin(),
			req.Filter,
			req.CustomerServiceId,
			&req.Sorting,
			&req.Pagination,
		)
		if err != nil {
			ctx.Error(err)
			return
		}

		// Return response with pagination
		ctx.OK(&result, pagination)
	}).Build()
