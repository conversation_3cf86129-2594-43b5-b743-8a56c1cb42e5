package cs_chat

import (
	"time"

	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/whatsapp_service"
)

var CronCsMessageSend = metagin.Cron("SendMessageCron").
	EverySecond(5).
	Handler(func(db *metaorm.DB) {
		messages, err := new(entities.Message).
			RepoWithoutWorkspace(db.Preload("Conversation", "Conversation.ChatChannel")).
			FindAll(db.And(
				db.Eq("status", configs.MessageStatusPending),
				db.Or(
					db.Lte("retry_at", time.Now().Unix()),
					db.IsNull("retry_at"),
				),
			))
		if err != nil {
			logger.Error("CronSendMessage: Failed to get messages", err)
			return
		}
		if len(messages) == 0 {
			return
		}
		for i := range messages {
			if messages[i].Retry.Get() >= 3 {
				messages[i].Status.Set(configs.MessageStatusFailed)
				continue
			}
			messages[i].Retry.Set(messages[i].Retry.Get() + 1)
			messages[i].RetryAt.Set(time.Now().Add(time.Duration(messages[i].Retry.Get()) * time.Minute).Unix())
			messages[i].Status.Set(configs.MessageStatusSending)
		}
		new(entities.Message).RepoWithoutWorkspace(db).SaveAll(messages)

		platformSender := whatsapp_service.NewPlatformSender(db)
		for _, message := range messages {
			platformSender.SendMessageAsync(&message, message.Conversation.ChatChannel, message.WorkspaceId.Get())
		}
	}).Build()
