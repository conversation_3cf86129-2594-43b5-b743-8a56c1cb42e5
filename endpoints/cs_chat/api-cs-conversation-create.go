package cs_chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/cs_chat_service"
)

var ApiCsConversationCreate = metagin.Post[cs_chat_service.CreateCsConversationRequest, entities.CsConversationDTO]("createCsConversation").
	Route("/cs/conversation").
	Handler(func(ctx metagin.IContext[cs_chat_service.CreateCsConversationRequest, entities.CsConversationDTO]) {
		// Get request data
		req := ctx.Request()

		// Create service manager
		manager := cs_chat_service.NewCsConversationManager(ctx.DB())

		if req.CustomerServiceId == 0 {
			req.CustomerServiceId = ctx.WorkspaceUserId()
		}

		// Create CS conversation
		result, err := manager.CreateCsConversation(req, ctx.WorkspaceId())
		if err != nil {
			ctx.Error(err)
			return
		}

		// Return response
		ctx.OK(result)
	}).Build()
