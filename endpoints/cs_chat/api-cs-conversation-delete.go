package cs_chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/services/cs_chat_service"
)

var ApiCsConversationDelete = metagin.Delete[base.RequestPathId, base.Empty]("deleteCsConversation").
	Route("/cs/conversation/:id").
	Handler(func(ctx metagin.IContext[base.RequestPathId, base.Empty]) {
		// Get request data
		req := ctx.Request()

		// Create service manager
		manager := cs_chat_service.NewCsConversationManager(ctx.DB())

		// Delete CS conversation
		err := manager.DeleteCsConversation(req.ID, ctx.WorkspaceId())
		if err != nil {
			ctx.Error(err)
			return
		}

		// Return success response
		ctx.OK(nil)
	}).Build()
