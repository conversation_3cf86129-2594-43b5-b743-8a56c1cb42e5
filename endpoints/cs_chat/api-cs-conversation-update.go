package cs_chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/cs_chat_service"
)

type UpdateCsConversationRequest struct {
	base.RequestPathId
	cs_chat_service.UpdateCsConversationRequest
}

var ApiCsConversationUpdate = metagin.Put[UpdateCsConversationRequest, entities.CsConversationDTO]("updateCsConversation").
	Route("/cs/conversation/:id").
	Handler(func(ctx metagin.IContext[UpdateCsConversationRequest, entities.CsConversationDTO]) {
		// Get request data
		req := ctx.Request()

		// Create service manager
		manager := cs_chat_service.NewCsConversationManager(ctx.DB())

		if req.UpdateCsConversationRequest.CustomerServiceId == nil || *req.UpdateCsConversationRequest.CustomerServiceId == 0 {
			workspaceUserId := ctx.WorkspaceUserId()
			req.UpdateCsConversationRequest.CustomerServiceId = &workspaceUserId
		}

		// Update CS conversation
		result, err := manager.UpdateCsConversation(req.ID, ctx.WorkspaceId(), &req.UpdateCsConversationRequest)
		if err != nil {
			ctx.Error(err)
			return
		}

		// Return updated conversation
		ctx.OK(result)
	}).Build()
