package cs_chat

import (
	"time"

	"github.com/gorilla/websocket"
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/cs_chat_service"
)

type CsConversationListMessage struct {
	Type string                       `json:"type"`
	Data []entities.CsConversationDTO `json:"data"`
	Page *metaorm.Pagination          `json:"page,omitempty"`
}

type CsConversationListRequest struct {
	metaorm.Sorting
	metaorm.Pagination
	// Primary filter field - for predefined filter scenarios
	Filter *string `form:"filter,omitempty" json:"filter,omitempty"`
	// Specific user filtering (still needed for granular control)
	CustomerServiceId *uint `form:"customer_service_id,omitempty" json:"customer_service_id,omitempty"`
}

// Message for marking conversation as read
type CsConversationReadMessage struct {
	Type           string `json:"type"`
	ConversationId uint   `json:"conversation_id"`
}

// Union type for handling both message types
type CsConversationMessage struct {
	Type           string `json:"type"`
	ConversationId *uint  `json:"conversation_id,omitempty"`
	// Fields from CsConversationListRequest
	metaorm.Sorting
	metaorm.Pagination
	Filter            *string `form:"filter,omitempty" json:"filter,omitempty"`
	CustomerServiceId *uint   `form:"customer_service_id,omitempty" json:"customer_service_id,omitempty"`
}

// Track conversation state for change detection
type ConversationState struct {
	ID            uint    `json:"id"`
	LastMessageID *uint   `json:"last_message_id"`
	Pinned        bool    `json:"pinned"`
	ClosedAt      *string `json:"closed_at"`
	UnreadCount   uint    `json:"unread_count"`
}

var WsCsConversationList = metagin.WebSocket[CsConversationMessage, CsConversationListMessage]("csConversationList").
	Route("/cs/conversation").
	Handler(func(ctx metagin.IAuthWebSocketContext, ws *websocket.Conn) {
		ctx.LogInfo("CS conversation list WebSocket connection established", "userID", ctx.UserId(), "workspaceID", ctx.WorkspaceId())

		manager := cs_chat_service.NewCsConversationManager(ctx.DB())

		// Parse query parameters for initial filters using framework binding
		var initialReq CsConversationListRequest
		if err := ctx.GinContext().ShouldBindQuery(&initialReq); err != nil {
			ctx.LogError("Failed to bind query parameters", "error", err)
		}

		// Helper function to get current conversation states
		getCurrentConversationStates := func(req *CsConversationListRequest) ([]ConversationState, error) {
			conversations, _, err := manager.ListCsConversations(
				ctx.WorkspaceId(),
				ctx.WorkspaceUserId(),
				ctx.IsAdmin(),
				req.Filter,
				req.CustomerServiceId,
				&req.Sorting,
				&req.Pagination,
			)
			if err != nil {
				return nil, err
			}

			var states []ConversationState
			for _, conv := range conversations {
				state := ConversationState{
					ID:          conv.ID,
					Pinned:      conv.Pinned,
					UnreadCount: conv.UnreadCount,
				}

				// Get last message ID from LastMessage if available
				if conv.LastMessage != nil {
					state.LastMessageID = &conv.LastMessage.ID
				}

				// Convert ClosedAt timestamp to string if not zero
				if conv.ClosedAt != 0 {
					closedAtTime := time.Unix(conv.ClosedAt, 0)
					closedAtStr := closedAtTime.Format(time.RFC3339)
					state.ClosedAt = &closedAtStr
				}
				states = append(states, state)
			}
			return states, nil
		}

		// Send conversation list function
		sendConversationList := func(req *CsConversationListRequest) {
			// Validate customer_service_id belongs to workspace if provided
			if req.CustomerServiceId != nil {
				valid, err := manager.ValidateCustomerServiceBelongsToWorkspace(ctx.WorkspaceId(), *req.CustomerServiceId)
				if err != nil {
					ctx.LogError("Failed to validate customer service", "error", err)
					response := CsConversationListMessage{
						Type: "error",
						Data: nil,
					}
					if err := ws.WriteJSON(response); err != nil {
						ctx.LogError("Failed to send error message", "error", err)
					}
					return
				}
				if !valid {
					ctx.LogWarn("Customer service does not belong to workspace", "customerServiceId", *req.CustomerServiceId, "workspaceId", ctx.WorkspaceId())
					response := CsConversationListMessage{
						Type: "error",
						Data: nil,
					}
					if err := ws.WriteJSON(response); err != nil {
						ctx.LogError("Failed to send error message", "error", err)
					}
					return
				}
			}

			conversations, page, err := manager.ListCsConversations(
				ctx.WorkspaceId(),
				ctx.WorkspaceUserId(),
				ctx.IsAdmin(),
				req.Filter,
				req.CustomerServiceId,
				&req.Sorting,
				&req.Pagination,
			)
			if err != nil {
				ctx.LogError("Failed to fetch conversations", "error", err)
				response := CsConversationListMessage{
					Type: "error",
					Data: nil,
				}
				if err := ws.WriteJSON(response); err != nil {
					ctx.LogError("Failed to send error message", "error", err)
				}
				return
			}

			response := CsConversationListMessage{
				Type: "list",
				Data: conversations,
				Page: page,
			}

			if err := ws.WriteJSON(response); err != nil {
				ctx.LogError("Failed to send conversation list", "error", err)
			}
		}

		// Send initial list with query parameter filters
		sendConversationList(&initialReq)

		// Track last known conversation states
		var lastConversationStates []ConversationState
		initialStates, err := getCurrentConversationStates(&initialReq)
		if err != nil {
			ctx.LogError("Failed to get initial conversation states", "error", err)
		} else {
			lastConversationStates = initialStates
		}

		// Create a ticker for polling every 3 seconds
		ticker := time.NewTicker(3 * time.Second)
		defer ticker.Stop()

		// Create a done channel to signal when to stop
		done := make(chan bool)

		// Start monitoring for conversation changes in a goroutine
		go func() {
			for {
				select {
				case <-ticker.C:
					// Get current conversation states
					currentStates, err := getCurrentConversationStates(&initialReq)
					if err != nil {
						ctx.LogError("Failed to get current conversation states", "error", err)
						continue
					}

					// Check if states have changed
					stateChanged := conversationStatesChanged(lastConversationStates, currentStates)

					// If state has changed, send updated conversation list
					if stateChanged {
						ctx.LogDebug("Conversation states changed", "oldStatesCount", len(lastConversationStates), "newStatesCount", len(currentStates))
						sendConversationList(&initialReq)
						lastConversationStates = currentStates
					}
				case <-done:
					return
				}
			}
		}()

		// Listen for client requests
		for {
			var msg CsConversationMessage
			if err := ws.ReadJSON(&msg); err != nil {
				ctx.LogError("Failed to read WebSocket message", "error", err)
				break
			}

			ctx.LogDebug("Received WebSocket message", "type", msg.Type, "message", msg)

			switch msg.Type {
			case "read":
				// Handle read message
				if msg.ConversationId == nil {
					ctx.LogError("Missing conversation_id for read message")
					response := CsConversationListMessage{
						Type: "error",
						Data: nil,
					}
					if err := ws.WriteJSON(response); err != nil {
						ctx.LogError("Failed to send error message", "error", err)
					}
					continue
				}

				// Mark conversation as read
				err := manager.MarkConversationAsRead(*msg.ConversationId, ctx.WorkspaceId(), ctx.UserId(), ctx.IsAdmin())
				if err != nil {
					ctx.LogError("Failed to mark conversation as read", "error", err, "conversationId", *msg.ConversationId)
					response := CsConversationListMessage{
						Type: "error",
						Data: nil,
					}
					if err := ws.WriteJSON(response); err != nil {
						ctx.LogError("Failed to send error message", "error", err)
					}
					continue
				}

				ctx.LogInfo("Marked conversation as read", "conversationId", *msg.ConversationId)

				// Send updated conversation list after marking as read
				sendConversationList(&initialReq)

				// Update conversation states for monitoring
				newStates, err := getCurrentConversationStates(&initialReq)
				if err != nil {
					ctx.LogError("Failed to get conversation states after read", "error", err)
				} else {
					lastConversationStates = newStates
				}

			default:
				// Handle list request (default behavior)
				req := CsConversationListRequest{
					Sorting:           msg.Sorting,
					Pagination:        msg.Pagination,
					Filter:            msg.Filter,
					CustomerServiceId: msg.CustomerServiceId,
				}

				ctx.LogDebug("Received conversation list request", "request", req)

				// Update the initial request parameters for monitoring
				initialReq = req

				// Reset conversation states for new filters
				newStates, err := getCurrentConversationStates(&initialReq)
				if err != nil {
					ctx.LogError("Failed to get conversation states for new request", "error", err)
				} else {
					lastConversationStates = newStates
				}

				// Send updated conversation list based on filters immediately
				sendConversationList(&req)
			}
		}

		// Signal the monitoring goroutine to stop
		done <- true

		ctx.LogInfo("CS conversation list WebSocket connection closed", "userID", ctx.UserId())
	}).Build()

// Helper function to compare conversation states for changes
func conversationStatesChanged(oldStates, newStates []ConversationState) bool {
	if len(oldStates) != len(newStates) {
		return true
	}

	// Create maps for easier comparison
	oldStateMap := make(map[uint]ConversationState)
	for _, state := range oldStates {
		oldStateMap[state.ID] = state
	}

	newStateMap := make(map[uint]ConversationState)
	for _, state := range newStates {
		newStateMap[state.ID] = state
	}

	// Check if any conversation states have changed
	for id, newState := range newStateMap {
		oldState, exists := oldStateMap[id]
		if !exists {
			// New conversation appeared
			return true
		}

		// Compare key fields that should trigger updates
		if !conversationStateEqual(oldState, newState) {
			return true
		}
	}

	// Check if any conversations disappeared
	for id := range oldStateMap {
		if _, exists := newStateMap[id]; !exists {
			return true
		}
	}

	return false
}

// Helper function to compare individual conversation states
func conversationStateEqual(a, b ConversationState) bool {
	// Compare last message ID
	if a.LastMessageID == nil && b.LastMessageID != nil {
		return false
	}
	if a.LastMessageID != nil && b.LastMessageID == nil {
		return false
	}
	if a.LastMessageID != nil && b.LastMessageID != nil && *a.LastMessageID != *b.LastMessageID {
		return false
	}

	// Compare pinned status
	if a.Pinned != b.Pinned {
		return false
	}

	// Compare unread count
	if a.UnreadCount != b.UnreadCount {
		return false
	}

	// Compare closed at
	if a.ClosedAt == nil && b.ClosedAt != nil {
		return false
	}
	if a.ClosedAt != nil && b.ClosedAt == nil {
		return false
	}
	if a.ClosedAt != nil && b.ClosedAt != nil && *a.ClosedAt != *b.ClosedAt {
		return false
	}

	return true
}
