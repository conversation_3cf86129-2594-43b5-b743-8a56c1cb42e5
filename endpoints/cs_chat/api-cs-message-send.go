package cs_chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/cs_chat_service"
)

var ApiCsMessageSend = metagin.Post[cs_chat_service.SendMessageRequest, entities.CsMessageDTO]("sendCsMessage").
	Route("/cs/message/send").
	Handler(func(ctx metagin.IContext[cs_chat_service.SendMessageRequest, entities.CsMessageDTO]) {
		req := ctx.Request()

		// Create message service and delegate to it
		messageManager := cs_chat_service.NewMessageManager(ctx.DB())
		result, err := messageManager.SendMessage(req, ctx.IsAdmin(), ctx.UserId(), ctx.WorkspaceId())
		if err != nil {
			ctx.Error(err)
			return
		}

		ctx.OK(result)
	}).Build()
