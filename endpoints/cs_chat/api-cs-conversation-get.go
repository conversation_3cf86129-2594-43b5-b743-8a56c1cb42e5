package cs_chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/cs_chat_service"
)

var ApiCsConversationGet = metagin.Get[base.RequestPathId, entities.CsConversationDTO]("getCsConversation").
	Route("/cs/conversation/:id").
	Handler(func(ctx metagin.IContext[base.RequestPathId, entities.CsConversationDTO]) {
		// Get path parameter
		req := ctx.Request()
		id := req.ID

		// Create service manager
		manager := cs_chat_service.NewCsConversationManager(ctx.DB())

		// Get CS conversation
		result, err := manager.GetCsConversation(id, ctx.WorkspaceId())
		if err != nil {
			ctx.Error(err)
			return
		}

		// Return response
		ctx.OK(result)
	}).Build()
