package whatsapp_broadcast

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
	relationshipEntities "github.com/metadiv-tech/mod_relationship/entities"
)

type CreateWhatsappBroadcastRequest struct {
	ChannelId   uint   `json:"channel_id"`
	CustomerIds []uint `json:"customer_ids"`

	Name string `json:"name"`

	TemplateId   uint                             `json:"template_id"`
	TemplateLang string                           `json:"template_lang"`
	HeaderParams []entities.WhatsappTemplateParam `json:"header_params"`
	BodyParams   []entities.WhatsappTemplateParam `json:"body_params"`
	FooterParams []entities.WhatsappTemplateParam `json:"footer_params"`

	StartAfter int64 `json:"start_after"`
}

func (r *CreateWhatsappBroadcastRequest) ToEntity() *entities.WhatsappBroadcast {
	broadcast := &entities.WhatsappBroadcast{}
	broadcast.Name.Set(r.Name)
	broadcast.ChannelId.Set(r.ChannelId)
	broadcast.TemplateId.Set(r.TemplateId)
	broadcast.TemplateLang.Set(r.TemplateLang)
	broadcast.HeaderParams.Set(r.HeaderParams)
	broadcast.BodyParams.Set(r.BodyParams)
	broadcast.FooterParams.Set(r.FooterParams)
	broadcast.StartAfter.Set(r.StartAfter)
	broadcast.Status.Set(configs.WhatsappBroadcastStatusSending)
	return broadcast
}

var ApiWhatsappBroadcastCreate = metagin.Post[CreateWhatsappBroadcastRequest, entities.WhatsappBroadcastDTO]("createWhatsappBroadcast").
	Route("/whatsapp/broadcast").
	Handler(func(ctx metagin.IContext[CreateWhatsappBroadcastRequest, entities.WhatsappBroadcastDTO]) {

		template, err := new(entities.WhatsappTemplate).Repo(ctx.DB().Preload("Contents"), ctx.WorkspaceId()).FindOne(ctx.DB().Eq("id", ctx.Request().TemplateId))
		if err != nil {
			ctx.Error(err)
			return
		}
		if template == nil {
			ctx.Error(errors.ErrWhatsappTemplateNotFound)
			return
		}

		var hasLang bool
		for _, content := range template.Contents {
			if content.Language.Get() == ctx.Request().TemplateLang {
				hasLang = true
				break
			}
		}
		if !hasLang {
			ctx.Error(errors.ErrWhatsappTemplateNotFound)
			return
		}

		channel, err := new(entities.ChatChannel).Repo(ctx.DB(), ctx.WorkspaceId()).FindOne(ctx.DB().Eq("id", ctx.Request().ChannelId))
		if err != nil {
			ctx.Error(err)
			return
		}
		if channel == nil {
			ctx.Error(errors.ErrChatChannelNotFound)
			return
		}
		if channel.Platform.Get() != configs.PlatformWhatsApp {
			ctx.Error(errors.ErrChatChannelNotFound)
			return
		}

		broadcast := ctx.Request().ToEntity()
		broadcast.SenderId.Set(ctx.WorkspaceUserId())
		broadcast, err = broadcast.Repo(ctx.DB(), ctx.WorkspaceId()).Save(broadcast)
		if err != nil {
			ctx.Error(err)
			return
		}

		ids := make([]any, 0)
		for _, id := range ctx.Request().CustomerIds {
			ids = append(ids, id)
		}
		customers, err := new(relationshipEntities.Customer).Repo(ctx.DB(), ctx.WorkspaceId()).FindAll(ctx.DB().In("id", ids))
		if err != nil {
			ctx.Error(err)
			return
		}

		if len(customers) == 0 {
			ctx.Error(errors.ErrBroadcastMustSelectAtLeastOneCustomer)
			return
		}

		jobs := make([]entities.WhatsappBroadcastJob, 0)
		for _, customer := range customers {
			job := &entities.WhatsappBroadcastJob{}
			job.CustomerId.Set(customer.ID)
			job.BroadcastId.Set(broadcast.ID)
			job.Status.Set(configs.WhatsappBroadcastJobStatusPending)
			jobs = append(jobs, *job)
		}

		if len(jobs) > 0 {
			jobs, err = new(entities.WhatsappBroadcastJob).Repo(ctx.DB(), ctx.WorkspaceId()).SaveAll(jobs)
			if err != nil {
				ctx.Error(err)
				return
			}
			broadcast.Jobs = jobs
		}
		ctx.OK(broadcast.DTO())
	}).Build()
