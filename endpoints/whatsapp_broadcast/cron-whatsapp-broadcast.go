package whatsapp_broadcast

import (
	"strings"
	"time"

	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/whatsapp_service"
)

var CronWhatsappBroadcast = metagin.Cron("cronWhatsappBroadcast").
	EverySecond(15).
	Handler(func(db *metaorm.DB) {

		broadcastJobs, _, err := new(entities.WhatsappBroadcastJob).RepoWithoutWorkspace(
			db.Joins("Broadcast").Preload("Customer", "Broadcast.Channel", "Broadcast.Template", "Broadcast.Template.Contents")).FindAllComplex(
			metaorm.Paginate(1, 75),
			metaorm.Sort("cs_whatsapp_broadcast_jobs.created_at", true),
			db.And(
				db.Eq("cs_whatsapp_broadcast_jobs.status", configs.WhatsappBroadcastJobStatusPending),
				db.Eq("Broadcast.status", configs.WhatsappBroadcastStatusSending),
				db.Lt("Broadcast.start_after", time.Now().Unix()),
			),
		)
		if err != nil {
			logger.Error(err)
			return
		}
		if len(broadcastJobs) == 0 {
			return
		}

		for i := range broadcastJobs {
			broadcastJobs[i].Status.Set(configs.WhatsappBroadcastJobStatusSending)
		}
		broadcastJobs, err = new(entities.WhatsappBroadcastJob).RepoWithoutWorkspace(db).SaveAll(broadcastJobs)
		if err != nil {
			logger.Error(err)
			return
		}

		for i := range broadcastJobs {
			// Use a function to handle each job with proper error handling
			processJob(db, &broadcastJobs[i])
		}

		// Save all job updates
		_, err = new(entities.WhatsappBroadcastJob).RepoWithoutWorkspace(db).SaveAll(broadcastJobs)
		if err != nil {
			logger.Error(err)
		}

		// update broadcast status
		broadcastIds := make(map[uint]bool)
		for _, job := range broadcastJobs {
			broadcastIds[job.BroadcastId.Get()] = true
		}
		for broadcastId := range broadcastIds {
			count, err := new(entities.WhatsappBroadcastJob).RepoWithoutWorkspace(db).Count(db.And(
				db.Eq("broadcast_id", broadcastId),
				db.Or(
					db.Eq("status", configs.WhatsappBroadcastJobStatusPending),
					db.Eq("status", configs.WhatsappBroadcastJobStatusSending), // Fixed: this should be job status
				),
			))
			if err != nil {
				logger.Error(err)
				return
			}
			if count == 0 {
				broadcast, err := new(entities.WhatsappBroadcast).RepoWithoutWorkspace(db).FindOne(db.Eq("id", broadcastId))
				if err != nil {
					logger.Error(err)
					return
				}
				if broadcast != nil {
					broadcast.Status.Set(configs.WhatsappBroadcastStatusCompleted)
					_, err = broadcast.Repo(db, broadcast.WorkspaceId.Get()).Save(broadcast)
					if err != nil {
						logger.Error(err)
						return
					}
				}
			}
		}
	}).Build()

func processJob(db *metaorm.DB, job *entities.WhatsappBroadcastJob) {
	// Wrap job processing in a defer to ensure status is always updated
	defer func() {
		if r := recover(); r != nil {
			logger.Error("Panic in processJob: %v", r)
			job.Result.Set("panic during processing")
			job.Status.Set(configs.WhatsappBroadcastJobStatusFailed)
		}
	}()

	if job.Broadcast == nil {
		job.Result.Set("unknown broadcast")
		job.Status.Set(configs.WhatsappBroadcastJobStatusFailed)
		return
	}

	if job.Broadcast.Template == nil {
		job.Result.Set("no template")
		job.Status.Set(configs.WhatsappBroadcastJobStatusFailed)
		return
	}

	var content *entities.WhatsappTemplateContent
	for j := range job.Broadcast.Template.Contents {
		if job.Broadcast.Template.Contents[j].Language.Get() == job.Broadcast.TemplateLang.Get() {
			content = &job.Broadcast.Template.Contents[j]
			break
		}
	}

	if content == nil {
		job.Result.Set("no template content")
		job.Status.Set(configs.WhatsappBroadcastJobStatusFailed)
		return
	}

	channel := job.Broadcast.Channel
	if channel == nil {
		job.Result.Set("no channel")
		job.Status.Set(configs.WhatsappBroadcastJobStatusFailed)
		return
	}

	customer := job.Customer
	if customer == nil {
		job.Result.Set("no customer")
		job.Status.Set(configs.WhatsappBroadcastJobStatusFailed)
		return
	}

	chat, err := new(entities.CsConversation).RepoWithoutWorkspace(db.Joins("ChatChannel").Preload("Conversation")).FindOne(db.And(
		db.Eq("cs_conversations.workspace_id", job.WorkspaceId.Get()),
		db.Eq("ChatChannel.platform", configs.PlatformWhatsApp),
		db.Eq("cs_conversations.customer_id", customer.ID),
	))
	if err != nil {
		logger.Error(err)
		job.Result.Set("error finding conversation: " + err.Error())
		job.Status.Set(configs.WhatsappBroadcastJobStatusFailed)
		return
	}
	if chat == nil {
		chat = new(entities.CsConversation)
		chat.CustomerId.Set(customer.ID)
		chat.ChatChannelId.Set(channel.ID)

		conv := new(entities.Conversation)
		conv.UUID.Generate()
		conv.ChatChannelId.Set(channel.ID)
		conv, err = conv.Repo(db).Save(conv)
		if err != nil {
			job.Result.Set("error creating conversation: " + err.Error())
			job.Status.Set(configs.WhatsappBroadcastJobStatusFailed)
			return
		}

		chat.ConversationId.Set(conv.ID)
		chat, err = chat.Repo(db, job.WorkspaceId.Get()).Save(chat)
		if err != nil {
			job.Result.Set("error saving cs conversation: " + err.Error())
			job.Status.Set(configs.WhatsappBroadcastJobStatusFailed)
			return
		}
	}

	csMessage := new(entities.CsMessage)
	csMessage.CsConversationId.Set(chat.ID)
	csMessage.CustomerServiceId.Set(job.Broadcast.SenderId.Get())

	message := new(entities.Message)
	message.UUID.Generate()
	message.ConversationId.Set(chat.ConversationId.Get())
	message.MessageType.Set(configs.MessageTypeTemplate)
	message.Timestamp.Set(time.Now().Unix())
	message.Status.Set(configs.MessageStatusPending)
	message.PlatformSenderId.Set(channel.PhoneNumberID.Get())
	message.PlatformRecipientId.Set(customer.WhatsappId.Get())
	message.TemplateName.Set(job.Broadcast.Template.Name.Get())
	message.TemplateLanguage.Set(job.Broadcast.TemplateLang.Get())

	// Safe parameter processing for header
	header := content.HeaderContent.Get()
	if job.Broadcast.HeaderParams.Get() != nil {
		headerParams := *job.Broadcast.HeaderParams.Get()
		for j := range headerParams {
			key := headerParams[j].Key
			value := headerParams[j].Value
			header = strings.Replace(header, "{{"+key+"}}", value, 1)
		}
		message.TemplateHeaderParams.Set(headerParams)
	}
	// Replace display_name placeholder with customer display name
	header = strings.ReplaceAll(header, "{{display_name}}", customer.DisplayName.Get())
	// Replace customer_email placeholder with customer email
	header = strings.ReplaceAll(header, "{{customer_email}}", customer.Email.Get())
	// Replace customer_phone placeholder with customer phone
	header = strings.ReplaceAll(header, "{{customer_phone}}", customer.Phone.Get())
	message.TemplateHeader.Set(header)

	// Safe parameter processing for body
	body := content.BodyContent.Get()
	if job.Broadcast.BodyParams.Get() != nil {
		bodyParams := *job.Broadcast.BodyParams.Get()
		for j := range bodyParams {
			key := bodyParams[j].Key
			value := bodyParams[j].Value
			body = strings.Replace(body, "{{"+key+"}}", value, 1)
		}
		message.TemplateBodyParams.Set(bodyParams)
	}
	// Replace display_name placeholder with customer display name
	body = strings.ReplaceAll(body, "{{display_name}}", customer.DisplayName.Get())
	// Replace customer_email placeholder with customer email
	body = strings.ReplaceAll(body, "{{customer_email}}", customer.Email.Get())
	// Replace customer_phone placeholder with customer phone
	body = strings.ReplaceAll(body, "{{customer_phone}}", customer.Phone.Get())
	message.TemplateBody.Set(body)

	// Safe parameter processing for footer
	footer := content.FooterContent.Get()
	if job.Broadcast.FooterParams.Get() != nil {
		footerParams := *job.Broadcast.FooterParams.Get()
		for j := range footerParams {
			key := footerParams[j].Key
			value := footerParams[j].Value
			footer = strings.Replace(footer, "{{"+key+"}}", value, 1)
		}
		message.TemplateFooterParams.Set(footerParams)
	}
	message.TemplateFooter.Set(footer)

	// Save message first
	message, err = message.Repo(db, job.WorkspaceId.Get()).Save(message)
	if err != nil {
		logger.Error(err)
		job.Result.Set("error saving message: " + err.Error())
		job.Status.Set(configs.WhatsappBroadcastJobStatusFailed)
		return
	}

	// Use platform sender to send message
	platformSender := whatsapp_service.NewPlatformSender(db)
	platformSender.SendMessageAsync(message, channel, job.WorkspaceId.Get())

	// Create CS message
	csMessage.MessageId.Set(message.ID)
	csMessage, err = csMessage.Repo(db, job.WorkspaceId.Get()).Save(csMessage)
	if err != nil {
		logger.Error(err)
		job.Result.Set("error saving cs message: " + err.Error())
		job.Status.Set(configs.WhatsappBroadcastJobStatusFailed)
		return
	}

	// Update conversation last message
	chat.LastMessageId.Set(csMessage.ID)
	_, err = chat.Repo(db, job.WorkspaceId.Get()).Save(chat)
	if err != nil {
		logger.Error(err)
		job.Result.Set("error updating conversation: " + err.Error())
		job.Status.Set(configs.WhatsappBroadcastJobStatusFailed)
		return
	}

	job.Result.Set("sent to " + customer.WhatsappId.Get())
	job.Status.Set(configs.WhatsappBroadcastJobStatusSent)
}
