package whatsapp_broadcast

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/entities"
)

type BroadcastJobListRequest struct {
	base.RequestPathId
	base.RequestListing
}

var ApiWhatsappBroadcastJobList = metagin.Get[BroadcastJobListRequest, []entities.WhatsappBroadcastJobDTO]("getWhatsappBroadcastJobList").
	Route("/whatsapp/broadcast/:id/job").
	Handler(func(ctx metagin.IContext[BroadcastJobListRequest, []entities.WhatsappBroadcastJobDTO]) {
		jobs, page, err := new(entities.WhatsappBroadcastJob).Repo(ctx.DB().Preload("Customer"), ctx.WorkspaceId()).FindAllComplex(
			&ctx.Request().Pagination,
			&ctx.Request().Sorting,
			ctx.DB().Eq("broadcast_id", ctx.Request().ID),
		)
		if err != nil {
			ctx.Error(err)
			return
		}
		ds := make([]entities.WhatsappBroadcastJobDTO, 0)
		for _, job := range jobs {
			ds = append(ds, *job.DTO())
		}
		ctx.OK(&ds, page)
	}).Build()
