package whatsapp_broadcast

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
)

type UpdateBroadcastJobRequest struct {
	base.RequestPathId
	JobId  uint   `uri:"job_id" json:"-"`
	Status string `json:"status"`
}

var ApiWhatsappBroadcastJobUpdate = metagin.Put[UpdateBroadcastJobRequest, entities.WhatsappBroadcastJobDTO]("updateWhatsappBroadcastJob").
	Route("/whatsapp/broadcast/:id/job/:job_id").
	Handler(func(ctx metagin.IContext[UpdateBroadcastJobRequest, entities.WhatsappBroadcastJobDTO]) {
		job, err := new(entities.WhatsappBroadcastJob).Repo(ctx.DB(), ctx.WorkspaceId()).FindOne(ctx.DB().And(
			ctx.DB().Eq("id", ctx.Request().JobId),
			ctx.DB().Eq("broadcast_id", ctx.Request().ID),
		))
		if err != nil {
			ctx.Error(err)
			return
		}
		if job == nil {
			ctx.Error(errors.ErrWhatsappBroadcastJobNotFound)
			return
		}

		if job.Status.Get() != configs.WhatsappBroadcastJobStatusSent &&
			job.Status.Get() != configs.WhatsappBroadcastStatusSending {
			job.Status.Set(ctx.Request().Status)
		}
		job, err = job.Repo(ctx.DB(), ctx.WorkspaceId()).Save(job)
		if err != nil {
			ctx.Error(err)
			return
		}

		if job.Status.Get() == configs.WhatsappBroadcastJobStatusCancelled {
			count, err := new(entities.WhatsappBroadcastJob).Repo(ctx.DB(), ctx.WorkspaceId()).Count(ctx.DB().And(
				ctx.DB().Eq("broadcast_id", job.BroadcastId),
				ctx.DB().Or(
					ctx.DB().Eq("status", configs.WhatsappBroadcastJobStatusPending),
					ctx.DB().Eq("status", configs.WhatsappBroadcastStatusSending),
				),
			))
			if err != nil {
				ctx.Error(err)
				return
			}
			count2, err := new(entities.WhatsappBroadcastJob).Repo(ctx.DB(), ctx.WorkspaceId()).Count(ctx.DB().And(
				ctx.DB().Eq("broadcast_id", job.BroadcastId),
				ctx.DB().Eq("status", configs.WhatsappBroadcastJobStatusSent),
			))
			if err != nil {
				ctx.Error(err)
				return
			}
			if count == 0 {
				broadcast, err := new(entities.WhatsappBroadcast).Repo(ctx.DB(), ctx.WorkspaceId()).FindOne(ctx.DB().Eq("id", job.BroadcastId))
				if err != nil {
					ctx.Error(err)
					return
				}
				if broadcast != nil {
					if count2 == 0 {
						broadcast.Status.Set(configs.WhatsappBroadcastStatusCancelled)
					} else {
						broadcast.Status.Set(configs.WhatsappBroadcastStatusCompleted)
					}
					_, err = broadcast.Repo(ctx.DB(), ctx.WorkspaceId()).Save(broadcast)
					if err != nil {
						ctx.Error(err)
						return
					}
				}
			}
		}
		ctx.OK(job.DTO())
	}).Build()
