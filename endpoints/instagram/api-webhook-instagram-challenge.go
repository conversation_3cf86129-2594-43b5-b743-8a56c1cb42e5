package instagram

import (
	"time"

	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
)

type InstagramWebhookChallengeRequest struct {
	HubChallenge   string `form:"hub.challenge" json:"-"`
	HubVerifyToken string `form:"hub.verify_token" json:"-"`
}

var ApiInstagramChallenge = metagin.Get[InstagramWebhookChallengeRequest, base.Empty]("instagramChallenge").
	PublicRoute("/cs/instagram/webhook").
	Handler(func(ctx metagin.IPublicContext[InstagramWebhookChallengeRequest, base.Empty]) {
		channel, err := new(entities.ChatChannel).RepoWithoutWorkspace(ctx.DB()).FindOne(ctx.DB().Eq("secret", ctx.Request().HubVerifyToken))
		if err != nil {
			ctx.Error(err)
			return
		}
		if channel == nil {
			ctx.Error(errors.ErrInvalidVerifyToken)
			return
		}
		if channel.Platform.Get() != configs.PlatformInstagram {
			ctx.Error(errors.ErrInvalidVerifyToken)
			return
		}
		ctx.String(ctx.Request().HubChallenge)
	}).
	WithRateLimit(time.Minute, 300).
	SkipTypescript().
	Build()
