package instagram

import (
	"github.com/metadiv-tech/instagram_api"
	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/services/cs_chat_service"
	"github.com/metadiv-tech/mod_chat/services/instagram_service"
)

type InstagramWebhookPayload struct {
	instagram_api.WebhookPayload
}

var ApiInstagramWebhook = metagin.Post[InstagramWebhookPayload, base.Empty]("csInstagramWebhook").
	PublicRoute("/cs/instagram/webhook").
	Handler(func(ctx metagin.IPublicContext[InstagramWebhookPayload, base.Empty]) {

		logger.Debug("Instagram webhook received:\n", ctx.RawRequest())

		payload := ctx.Request()

		// Register CS post webhook handler
		cs_chat_service.RegisterCsPostWebhookHandler(ctx.DB())

		// Register Instagram services
		instagram_service.RegisterInstagramServices(ctx.DB())

		// Create webhook handler service
		serviceWebhookHandler := instagram_service.NewWebhookHandler(ctx.DB())

		// Process Instagram webhook payload
		err := serviceWebhookHandler.HandleIncomingMessages(&payload.WebhookPayload)
		if err != nil {
			logger.Error("Failed to handle Instagram webhook messages: %v", err)
			ctx.Error(err)
			return
		}

		ctx.OK(nil)
	}).Build()
