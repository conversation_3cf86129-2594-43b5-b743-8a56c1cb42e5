package whatsapp_template

import (
	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/whatsapp_api"
)

var CronWhatsappTemplateUpdatePending = metagin.Cron("updatePendingWhatsappTemplate").
	EveryMinute(1).
	Handler(func(db *metaorm.DB) {
		contents, err := new(entities.WhatsappTemplateContent).RepoWithoutWorkspace(
			db.Preload("Template").Preload("Template.Channel"),
		).FindAll(db.Eq("status", configs.WhatsappTemplateStatusPending))
		if err != nil {
			logger.Error("whatsapp template update pending", "error", err)
			return
		}

		for i, content := range contents {
			wsClient, err := whatsapp_api.NewClient(content.Template.Channel.BusinessAccountID.Get(), content.Template.Channel.AccessToken.Get(), content.Template.Channel.PhoneNumberID.Get())
			if err != nil {
				logger.Error("whatsapp template update pending", "error", err)
				continue
			}
			resp, err := wsClient.Template().Get(content.WaID.Get())
			if err != nil {
				logger.Error("whatsapp template update pending", "error", err)
				continue
			}
			contents[i].Status.Set(resp.Status)
			contents[i].RepoWithoutWorkspace(db).Save(&contents[i])
		}
	}).Build()
