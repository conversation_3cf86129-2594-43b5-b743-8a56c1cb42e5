package whatsapp_template

import (
	"errors"

	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/whatsapp_api"
)

var ApiWhatsappTemplateDelete = metagin.Delete[base.RequestPathId, base.Empty]("deleteWhatsappTemplate").
	Route("/whatsapp/template/:id").
	Handler(func(ctx metagin.IContext[base.RequestPathId, base.Empty]) {
		template, err := new(entities.WhatsappTemplate).Repo(ctx.DB().Preload("Channel"), ctx.WorkspaceId()).FindOne(
			ctx.DB().Eq("id", ctx.Request().ID),
		)
		if err != nil {
			ctx.Error(err)
			return
		}
		if template == nil {
			ctx.Error(errors.New("template not found"))
			return
		}
		if template.Channel != nil {
			wsClient, err := whatsapp_api.NewClient(template.Channel.BusinessAccountID.Get(), template.Channel.AccessToken.Get(), template.Channel.PhoneNumberID.Get())
			if err != nil {
				ctx.Error(err)
				return
			}
			err = wsClient.Template().Delete(&whatsapp_api.DeleteTemplateRequest{
				Name: template.Name.Get(),
			})
			if err != nil {
				ctx.LogError(err)
			}
		}

		new(entities.WhatsappTemplate).Repo(ctx.DB(), ctx.WorkspaceId()).Delete(template)
		ctx.OK(nil)
	}).Build()
