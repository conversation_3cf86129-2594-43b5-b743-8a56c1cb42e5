package whatsapp_template

import (
	"strings"

	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/whatsapp_api"
)

type WhatsappTemplateLanguageRequest struct {
	Keyword string `form:"keyword" json:"-"`
}

type WhatsappTemplateLanguageDTO struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

var ApiWhatsappTemplateLanguageList = metagin.Get[WhatsappTemplateLanguageRequest, []WhatsappTemplateLanguageDTO]("listWhatsappTemplateLanguage").
	Route("/whatsapp/template/language").
	Handler(func(ctx metagin.IContext[WhatsappTemplateLanguageRequest, []WhatsappTemplateLanguageDTO]) {
		ds := make([]WhatsappTemplateLanguageDTO, 0)
		for _, v := range whatsapp_api.Languages {
			if strings.Contains(strings.ToLower(v.NameEn), strings.ToLower(ctx.Request().Keyword)) ||
				strings.Contains(strings.ToLower(v.NameZhHk), strings.ToLower(ctx.Request().Keyword)) ||
				strings.Contains(strings.ToLower(v.NameZhCn), strings.ToLower(ctx.Request().Keyword)) ||
				strings.Contains(strings.ToLower(v.Code), strings.ToLower(ctx.Request().Keyword)) {
				d := WhatsappTemplateLanguageDTO{Code: v.Code}
				switch ctx.Locale() {
				case "en":
					d.Name = v.NameEn
					ds = append(ds, d)
				case "zh-hk":
					d.Name = v.NameZhHk
					ds = append(ds, d)
				case "zh-cn":
					d.Name = v.NameZhCn
					ds = append(ds, d)
				}
			}
		}
		ctx.OK(&ds)
	}).Build()
