package whatsapp_template

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
	"github.com/metadiv-tech/whatsapp_api"
)

type WhatsappTemplateUpdateRequest struct {
	WhatsappTemplateCreateRequest
}

var ApiWhatsappTemplateUpdate = metagin.Put[WhatsappTemplateUpdateRequest, entities.WhatsappTemplateContentDTO]("updateWhatsappTemplate").
	Route("/whatsapp/template").
	Handler(func(ctx metagin.IContext[WhatsappTemplateUpdateRequest, entities.WhatsappTemplateContentDTO]) {

		template, err := new(entities.WhatsappTemplate).Repo(ctx.DB().Preload("Channel"), ctx.WorkspaceId()).FindOne(ctx.DB().And(
			ctx.DB().Eq("channel_id", ctx.Request().ChannelId),
			ctx.DB().Eq("*name", ctx.Request().Name),
		))
		if err != nil {
			ctx.Error(err)
			return
		}
		if template == nil {
			ctx.Error(errors.ErrWhatsappTemplateNotFound)
			return
		}
		if template.Channel == nil {
			ctx.Error(errors.ErrChatChannelNotFound)
			return
		}
		template = ctx.Request().ToTemplateEntity(template)
		template, err = new(entities.WhatsappTemplate).Repo(ctx.DB(), ctx.WorkspaceId()).Save(template)
		if err != nil {
			ctx.Error(err)
			return
		}

		content, err := new(entities.WhatsappTemplateContent).Repo(ctx.DB(), ctx.WorkspaceId()).FindOne(ctx.DB().And(
			ctx.DB().Eq("template_id", template.ID),
			ctx.DB().Eq("language", ctx.Request().Language),
		))
		if err != nil {
			ctx.Error(err)
			return
		}
		content = ctx.Request().ToContentEntity(content)

		wsClient, err := whatsapp_api.NewClient(template.Channel.BusinessAccountID.Get(), template.Channel.AccessToken.Get(), template.Channel.PhoneNumberID.Get())
		if err != nil {
			ctx.Error(err)
			return
		}

		var mediaId string
		if content.HeaderType.Get() == configs.WhatsappTemplateHeaderTypeImage ||
			content.HeaderType.Get() == configs.WhatsappTemplateHeaderTypeVideo ||
			content.HeaderType.Get() == configs.WhatsappTemplateHeaderTypeDocument {

			// Upload media to WhatsApp and get media ID
			mediaId, err = uploadMediaToWhatsApp(wsClient, ctx.Request().HeaderContent, ctx.Request().HeaderMediaFile)
			if err != nil {
				ctx.Error(err)
				return
			}
		}

		resp, err := wsClient.Template().Update(content.WaID.Get(), &whatsapp_api.TemplateUpdateRequest{
			Name:       template.Name.Get(),
			Category:   ctx.Request().Category,
			Components: prepareComponents(content, ctx.Request().HeaderParams, ctx.Request().BodyParams, mediaId),
		})
		if err != nil {
			ctx.Error(err)
			return
		}

		content.TemplateId.Set(template.ID)
		content.HeaderContent.Set(ctx.Request().HeaderContent) // store the original header content
		content.WaID.Set(resp.ID)
		content.Status.Set(configs.WhatsappTemplateStatusPending)
		content, err = new(entities.WhatsappTemplateContent).Repo(ctx.DB(), ctx.WorkspaceId()).Save(content)
		if err != nil {
			ctx.Error(err)
			return
		}

		content.Template = template
		ctx.OK(content.DTO())
	}).Build()
