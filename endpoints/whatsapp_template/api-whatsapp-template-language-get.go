package whatsapp_template

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/errors"
	"github.com/metadiv-tech/whatsapp_api"
)

type GetWhatsappTemplateLanguageRequest struct {
	Code string `uri:"code" json:"-"`
}

var ApiWhatsappTemplateLanguageGet = metagin.Get[GetWhatsappTemplateLanguageRequest, WhatsappTemplateLanguageDTO]("getWhatsappTemplateLanguage").
	Route("/whatsapp/template/language/:code").
	Handler(func(ctx metagin.IContext[GetWhatsappTemplateLanguageRequest, WhatsappTemplateLanguageDTO]) {
		for _, v := range whatsapp_api.Languages {
			if v.Code == ctx.Request().Code {
				switch ctx.Locale() {
				case "en":
					ctx.OK(&WhatsappTemplateLanguageDTO{Code: v.Code, Name: v.NameEn})
				case "zh-hk":
					ctx.OK(&WhatsappTemplateLanguageDTO{Code: v.Code, Name: v.NameZhHk})
				case "zh-cn":
					ctx.OK(&WhatsappTemplateLanguageDTO{Code: v.Code, Name: v.NameZhCn})
				}
				return
			}
		}
		ctx.Error(errors.ErrWhatsappTemplateLanguageNotFound)
	}).Build()
