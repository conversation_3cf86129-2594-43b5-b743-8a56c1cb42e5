package whatsapp_template

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/whatsapp_api"
)

type WhatsappTemplateTemplateCategoryDTO struct {
	Name string `json:"name"`
}

var ApiWhatsappTemplateCategoryList = metagin.Get[base.Empty, []WhatsappTemplateTemplateCategoryDTO]("listWhatsappTemplateCategory").
	Route("/whatsapp/template/category").
	Handler(func(ctx metagin.IContext[base.Empty, []WhatsappTemplateTemplateCategoryDTO]) {
		types := []WhatsappTemplateTemplateCategoryDTO{
			{Name: whatsapp_api.TemplateCategoryUtility},
			{Name: whatsapp_api.TemplateCategoryMarketing},
			{Name: whatsapp_api.TemplateCategoryAuthentication},
		}
		ctx.OK(&types)
	}).Build()
