package whatsapp_template

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/entities"
)

type WhatsappTemplateListRequest struct {
	base.RequestListing
	ChannelId uint `json:"-" uri:"channel_id"`
}

var ApiWhatsappTemplateList = metagin.Get[WhatsappTemplateListRequest, []entities.WhatsappTemplateDTO]("getWhatsappTemplateList").
	Route("/whatsapp/template/list/:channel_id").
	Handler(func(ctx metagin.IContext[WhatsappTemplateListRequest, []entities.WhatsappTemplateDTO]) {
		templates, page, err := new(entities.WhatsappTemplate).Repo(ctx.DB().Preload("Contents").Preload("Channel"), ctx.WorkspaceId()).FindAllComplex(
			&ctx.Request().Pagination,
			&ctx.Request().Sorting,
			ctx.DB().And(
				ctx.DB().Eq("channel_id", ctx.Request().ChannelId),
				ctx.Request().SimilarKeyword("*name"),
			),
		)
		if err != nil {
			ctx.Error(err)
			return
		}
		ds := make([]entities.WhatsappTemplateDTO, 0)
		for _, template := range templates {
			ds = append(ds, *template.DTO())
		}
		ctx.OK(&ds, page)
	}).Build()
