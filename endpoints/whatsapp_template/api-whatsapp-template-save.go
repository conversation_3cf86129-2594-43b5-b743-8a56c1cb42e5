package whatsapp_template

import (
	"encoding/base64"
	"fmt"
	"strings"

	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
	"github.com/metadiv-tech/mod_chat/services/whatsapp_template_service"
	"github.com/metadiv-tech/whatsapp_api"
)

type WhatsappTemplateCreateRequest struct {
	ChannelId uint   `json:"channel_id"`
	Name      string `json:"name"`

	Language string `json:"language"`
	Category string `json:"category"`

	HeaderType      string                           `json:"header_type"`
	HeaderMediaFile string                           `json:"header_media_file"`
	HeaderContent   string                           `json:"header_content"`
	HeaderParams    []entities.WhatsappTemplateParam `json:"header_params"`

	BodyContent string                           `json:"body_content"`
	BodyParams  []entities.WhatsappTemplateParam `json:"body_params"`

	FooterContent string `json:"footer_content"`
}

func (r *WhatsappTemplateCreateRequest) Validate() error {
	if r.Name == "" {
		return errors.ErrWhatsappTemplateNameRequired
	}
	if !whatsapp_template_service.CheckLanguage(r.Language) {
		return errors.ErrWhatsappTemplateLanguageInvalid
	}
	if !whatsapp_template_service.CheckCategory(r.Category) {
		return errors.ErrWhatsappTemplateCategoryInvalid
	}
	return nil
}

func (r *WhatsappTemplateCreateRequest) ToTemplateEntity(e *entities.WhatsappTemplate) *entities.WhatsappTemplate {
	if e == nil {
		e = &entities.WhatsappTemplate{}
		e.Name.Set(r.Name)
		e.ChannelId.Set(r.ChannelId)
	}
	e.Category.Set(r.Category)
	return e
}

func (r *WhatsappTemplateCreateRequest) ToContentEntity(e *entities.WhatsappTemplateContent) *entities.WhatsappTemplateContent {
	if e == nil {
		e = &entities.WhatsappTemplateContent{}
	}
	e.Language.Set(r.Language)
	e.HeaderType.Set(r.HeaderType)
	e.HeaderMediaFile.Set(r.HeaderMediaFile)
	e.HeaderContent.Set(r.HeaderContent)
	e.HeaderParams.Set(r.HeaderParams)
	e.BodyContent.Set(r.BodyContent)
	e.BodyParams.Set(r.BodyParams)
	e.FooterContent.Set(r.FooterContent)
	return e
}

// uploadMediaToWhatsApp uploads base64 encoded media content and returns the media ID
func uploadMediaToWhatsApp(client *whatsapp_api.Client, base64Content, filename string) (string, error) {
	// Decode base64 content
	mediaBytes, err := base64.StdEncoding.DecodeString(base64Content)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64 media content: %w", err)
	}

	// Upload media to WhatsApp
	uploadResponse, err := client.Media().Upload(filename, mediaBytes)
	if err != nil {
		return "", fmt.Errorf("failed to upload media to WhatsApp: %w", err)
	}

	return uploadResponse.ID, nil
}

var ApiWhatsappTemplateSave = metagin.Post[WhatsappTemplateCreateRequest, entities.WhatsappTemplateContentDTO]("saveWhatsappTemplate").
	Route("/whatsapp/template").
	Handler(func(ctx metagin.IContext[WhatsappTemplateCreateRequest, entities.WhatsappTemplateContentDTO]) {

		// check channel exists
		channel, err := new(entities.ChatChannel).Repo(ctx.DB(), ctx.WorkspaceId()).FindOne(ctx.DB().Eq("id", ctx.Request().ChannelId))
		if err != nil {
			ctx.Error(err)
			return
		}
		if channel == nil {
			ctx.Error(errors.ErrChatChannelNotFound)
			return
		}

		template, err := new(entities.WhatsappTemplate).Repo(ctx.DB().Preload("Contents").Preload("Channel"), ctx.WorkspaceId()).FindOne(ctx.DB().And(
			ctx.DB().Eq("channel_id", ctx.Request().ChannelId),
			ctx.DB().Eq("*name", ctx.Request().Name),
		))
		if err != nil {
			ctx.Error(err)
			return
		}
		template = ctx.Request().ToTemplateEntity(template)
		template, err = new(entities.WhatsappTemplate).Repo(ctx.DB(), ctx.WorkspaceId()).Save(template)
		if err != nil {
			ctx.Error(err)
			return
		}

		content, err := new(entities.WhatsappTemplateContent).Repo(ctx.DB(), ctx.WorkspaceId()).FindOne(ctx.DB().And(
			ctx.DB().Eq("template_id", template.ID),
			ctx.DB().Eq("language", ctx.Request().Language),
		))
		if err != nil {
			ctx.Error(err)
			return
		}
		content = ctx.Request().ToContentEntity(content)

		wsClient, err := whatsapp_api.NewClient(channel.BusinessAccountID.Get(), channel.AccessToken.Get(), channel.PhoneNumberID.Get())
		if err != nil {
			ctx.Error(err)
			return
		}

		var mediaId string
		if content.HeaderType.Get() == configs.WhatsappTemplateHeaderTypeImage ||
			content.HeaderType.Get() == configs.WhatsappTemplateHeaderTypeVideo ||
			content.HeaderType.Get() == configs.WhatsappTemplateHeaderTypeDocument {

			// Upload media to WhatsApp and get media ID
			mediaId, err = uploadMediaToWhatsApp(wsClient, ctx.Request().HeaderContent, ctx.Request().HeaderMediaFile)
			if err != nil {
				ctx.Error(err)
				return
			}
		}

		var resp *whatsapp_api.TemplateResponse
		if content.WaID.Get() == "" {
			resp, err = wsClient.Template().Create(&whatsapp_api.TemplateCreateRequest{
				Name:       template.Name.Get(),
				Category:   ctx.Request().Category,
				Language:   content.Language.Get(),
				Components: prepareComponents(content, ctx.Request().HeaderParams, ctx.Request().BodyParams, mediaId),
			})
		} else {
			_, err = wsClient.Template().Update(content.WaID.Get(), &whatsapp_api.TemplateUpdateRequest{
				Name:       template.Name.Get(),
				Category:   ctx.Request().Category,
				Components: prepareComponents(content, ctx.Request().HeaderParams, ctx.Request().BodyParams, mediaId),
				Status:     configs.WhatsappTemplateStatusPending,
			})
			if err != nil {
				ctx.Error(err)
				if len(template.Contents) == 0 {
					new(entities.WhatsappTemplate).Repo(ctx.DB().Unscoped(), ctx.WorkspaceId()).Delete(template)
				}
				return
			}
			resp, err = wsClient.Template().Get(content.WaID.Get())
		}

		if err != nil {
			ctx.Error(err)
			if len(template.Contents) == 0 {
				new(entities.WhatsappTemplate).Repo(ctx.DB().Unscoped(), ctx.WorkspaceId()).Delete(template)
			}
			return
		}

		content.TemplateId.Set(template.ID)
		content.HeaderContent.Set(ctx.Request().HeaderContent) // store the original header content
		content.WaID.Set(resp.ID)
		content.Status.Set(resp.Status)
		content, err = new(entities.WhatsappTemplateContent).Repo(ctx.DB(), ctx.WorkspaceId()).Save(content)
		if err != nil {
			ctx.Error(err)
			return
		}

		content.Template = template
		ctx.OK(content.DTO())
	}).Build()

func handleTemplate(template string, params []entities.WhatsappTemplateParam) string {
	for i, param := range params {
		template = strings.Replace(template, "{{"+param.Key+"}}", fmt.Sprintf("{{%d}}", i+1), 1)
	}
	return template
}

func prepareComponents(
	content *entities.WhatsappTemplateContent,
	headerParams []entities.WhatsappTemplateParam,
	bodyParams []entities.WhatsappTemplateParam,
	mediaId string,
) []whatsapp_api.TemplateComponentRequest {
	components := make([]whatsapp_api.TemplateComponentRequest, 0)
	if content.HeaderContent.Get() != "" {
		var example *whatsapp_api.TemplateComponentExampleRequest = nil
		var format string
		var text string

		if content.HeaderType.Get() == configs.WhatsappTemplateHeaderTypeImage ||
			content.HeaderType.Get() == configs.WhatsappTemplateHeaderTypeVideo ||
			content.HeaderType.Get() == configs.WhatsappTemplateHeaderTypeDocument {
			// For media headers (image/video/document) - use media ID
			example = &whatsapp_api.TemplateComponentExampleRequest{
				HeaderHandle: []string{mediaId},
			}
			// Set the correct format based on header type
			switch content.HeaderType.Get() {
			case configs.WhatsappTemplateHeaderTypeImage:
				format = whatsapp_api.TemplateHeaderTypeImage
			case configs.WhatsappTemplateHeaderTypeVideo:
				format = whatsapp_api.TemplateHeaderTypeVideo
			case configs.WhatsappTemplateHeaderTypeDocument:
				format = whatsapp_api.TemplateHeaderTypeDocument
			}
			// Don't set text field for media headers
		} else {
			// For text headers
			format = whatsapp_api.TemplateHeaderTypeText
			text = handleTemplate(content.HeaderContent.Get(), headerParams)
			if len(headerParams) > 0 {
				example = &whatsapp_api.TemplateComponentExampleRequest{
					HeaderText: make([]string, 0),
				}
				for _, param := range headerParams {
					example.HeaderText = append(example.HeaderText, param.Value)
				}
			}
		}

		headerComponent := whatsapp_api.TemplateComponentRequest{
			Type:    whatsapp_api.TemplateHeader,
			Format:  format,
			Example: example,
		}

		// Only set text field for text headers
		if text != "" {
			headerComponent.Text = text
		}

		components = append(components, headerComponent)
	}
	if content.BodyContent.Get() != "" {
		var example *whatsapp_api.TemplateComponentExampleRequest = nil
		if len(bodyParams) > 0 {
			example = &whatsapp_api.TemplateComponentExampleRequest{
				BodyText: make([][]string, 0),
			}
			values := []string{}
			for _, param := range bodyParams {
				values = append(values, param.Value)
			}
			example.BodyText = append(example.BodyText, values)
		}
		components = append(components, whatsapp_api.TemplateComponentRequest{
			Type:    whatsapp_api.TemplateBody,
			Text:    handleTemplate(content.BodyContent.Get(), bodyParams),
			Example: example,
		})
	}
	if content.FooterContent.Get() != "" {
		// Footer components don't support parameters in WhatsApp API - they are always static text
		components = append(components, whatsapp_api.TemplateComponentRequest{
			Type: whatsapp_api.TemplateFooter,
			Text: content.FooterContent.Get(), // Use original content without parameter replacement
		})
	}
	return components
}
