package whatsapp_template

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/errors"
	"github.com/metadiv-tech/whatsapp_api"
)

type GetWhatsappTemplateCategoryRequest struct {
	Category string `uri:"category" json:"-"`
}

var ApiWhatsappTemplateCategoryGet = metagin.Get[GetWhatsappTemplateCategoryRequest, WhatsappTemplateTemplateCategoryDTO]("getWhatsappTemplateCategory").
	Route("/whatsapp/template/category/:category").
	Handler(func(ctx metagin.IContext[GetWhatsappTemplateCategoryRequest, WhatsappTemplateTemplateCategoryDTO]) {
		types := []WhatsappTemplateTemplateCategoryDTO{
			{Name: whatsapp_api.TemplateCategoryUtility},
			{Name: whatsapp_api.TemplateCategoryMarketing},
			{Name: whatsapp_api.TemplateCategoryAuthentication},
		}
		for _, v := range types {
			if v.Name == ctx.Request().Category {
				ctx.OK(&v)
				return
			}
		}
		ctx.Error(errors.ErrWhatsappTemplateCategoryNotFound)
	}).Build()
