package whatsapp_template

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
)

var ApiWhatsappTemplateGet = metagin.Get[base.RequestPathId, entities.WhatsappTemplateDTO]("getWhatsappTemplate").
	Route("/whatsapp/template/:id").
	Handler(func(ctx metagin.IContext[base.RequestPathId, entities.WhatsappTemplateDTO]) {
		template, err := new(entities.WhatsappTemplate).Repo(ctx.DB().Preload("Contents").Preload("Channel"), ctx.WorkspaceId()).FindOne(ctx.DB().Eq("id", ctx.Request().ID))
		if err != nil {
			ctx.Error(err)
			return
		}
		if template == nil {
			ctx.Error(errors.ErrWhatsappTemplateNotFound)
			return
		}
		ctx.OK(template.DTO())
	}).Build()
