package mod_chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/endpoints/whatsapp_broadcast"
	"github.com/metadiv-tech/mod_chat/entities"
)

var ModuleWhatsappBroadcast = metagin.NewModule("whatsapp-broadcast", "v1", func(m *metagin.Module) {
	m.RegisterMigration(
		&entities.WhatsappBroadcast{},
		&entities.WhatsappBroadcastJob{},
	)
	m.<PERSON>(
		whatsapp_broadcast.ApiWhatsappBroadcastList,
		whatsapp_broadcast.ApiWhatsappBroadcastGet,
		whatsapp_broadcast.ApiWhatsappBroadcastUpdate,
		whatsapp_broadcast.ApiWhatsappBroadcastCreate,
		whatsapp_broadcast.ApiWhatsappBroadcastJobList,
		whatsapp_broadcast.ApiWhatsappBroadcastJobUpdate,
		whatsapp_broadcast.CronWhatsappBroadcast,
	)
})
