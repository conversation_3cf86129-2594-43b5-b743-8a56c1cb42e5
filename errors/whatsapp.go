package errors

import "github.com/metadiv-tech/metagin"

var (
	ErrWhatsappTemplateCategoryNotFound = metagin.Error("whatsapp template category not found")
	ErrWhatsappTemplateNotFound         = metagin.Error("whatsapp template not found")
	ErrWhatsappTemplateLanguageNotFound = metagin.Error("whatsapp template language not found")
	ErrWhatsappTemplateNameRequired     = metagin.Error("whatsapp template name is required")
	ErrWhatsappTemplateLanguageInvalid  = metagin.Error("whatsapp template language is invalid")
	ErrWhatsappTemplateCategoryInvalid  = metagin.Error("whatsapp template category is invalid")
)

var (
	ErrBroadcastMustSelectAtLeastOneCustomer = metagin.Error("broadcast must select at least one customer")
	ErrWhatsappBroadcastNotFound             = metagin.Error("whatsapp broadcast not found")
	ErrWhatsappBroadcastJobNotFound          = metagin.Error("whatsapp broadcast job not found")
)
