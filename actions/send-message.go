package actions

import (
	"fmt"
	"strings"
	"time"

	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/chat_service"
	"github.com/metadiv-tech/mod_chat/services/whatsapp_service"
	relationshipEntities "github.com/metadiv-tech/mod_relationship/entities"
	"github.com/metadiv-tech/workflow"
)

type SendMessageRequest struct {
	CustomerId uint `json:"customer_id"`

	// Message type and content
	MessageType string `json:"message_type"` // text, image, video, audio, file, location

	// Text message
	Text string `json:"text,omitempty"`

	// Media messages (image, video, audio, file)
	Filename     string `json:"filename,omitempty"`
	MediaContent string `json:"media_content,omitempty"` // base64 encoded
}

type SendMessageResponse struct {
	Success   bool   `json:"success"`
	MessageId uint   `json:"message_id,omitempty"`
	Error     string `json:"error,omitempty"`
}

var SendMessage = workflow.NewAction[SendMessageRequest, SendMessageResponse]("send.message").
	WithCategory("WhatsApp").
	WithName("sendMessage").
	WithFunction(func(ctx *workflow.Context, input SendMessageRequest) (SendMessageResponse, error) {
		db := ctx.DB
		workspaceId := ctx.WorkspaceId

		// 1. Validate message type
		if !isValidMessageType(input.MessageType) {
			return SendMessageResponse{Success: false, Error: fmt.Sprintf("Invalid message type: %s", input.MessageType)}, nil
		}

		// 2. Validate message content based on type
		if err := validateMessageContent(&input); err != nil {
			return SendMessageResponse{Success: false, Error: err.Error()}, nil
		}

		// 3. Find and validate customer exists
		customer, err := new(relationshipEntities.Customer).Repo(db, workspaceId).FindOne(db.Eq("id", input.CustomerId))
		if err != nil {
			return SendMessageResponse{Success: false, Error: fmt.Sprintf("Error finding customer: %v", err)}, nil
		}
		if customer == nil {
			return SendMessageResponse{Success: false, Error: "Customer not found"}, nil
		}

		// Ensure customer has WhatsApp ID
		if customer.WhatsappId.Get() == "" {
			return SendMessageResponse{Success: false, Error: "Customer has no WhatsApp ID"}, nil
		}

		// 4. Find active WhatsApp session for this customer
		activeSession, err := findActiveWhatsAppSession(db, customer, workspaceId)
		if err != nil {
			return SendMessageResponse{Success: false, Error: fmt.Sprintf("Error finding active session: %v", err)}, nil
		}
		if activeSession == nil {
			return SendMessageResponse{Success: false, Error: "No active session found. Customer must initiate conversation within 24 hours to receive messages."}, nil
		}

		// 5. Create message based on type
		message, err := createMessageFromRequest(db, &input, activeSession.chat, activeSession.channel, customer, workspaceId)
		if err != nil {
			return SendMessageResponse{Success: false, Error: fmt.Sprintf("Error creating message: %v", err)}, nil
		}

		// 6. Send message using platform sender
		platformSender := whatsapp_service.NewPlatformSender(db)
		platformSender.SendMessageAsync(message, activeSession.channel, workspaceId)

		return SendMessageResponse{Success: true, MessageId: message.ID}, nil
	}).Build()

// activeSessionInfo holds information about an active customer session
type activeSessionInfo struct {
	chat    *entities.CsConversation
	channel *entities.ChatChannel
}

// findActiveWhatsAppSession finds an active WhatsApp session for the customer
// A session is considered active if the customer sent a message within the last 24 hours
func findActiveWhatsAppSession(db *metaorm.DB, customer *relationshipEntities.Customer, workspaceId uint) (*activeSessionInfo, error) {
	// Find CS conversations for this customer with WhatsApp channels
	conversations, err := new(entities.CsConversation).RepoWithoutWorkspace(db.
		Joins("ChatChannel").
		Joins("Conversation").
		Preload("ChatChannel").
		Preload("CustomerLastMessage").
		Preload("CustomerLastMessage.Message")).FindAll(db.And(
		db.Eq("cs_conversations.workspace_id", workspaceId),
		db.Eq("cs_conversations.customer_id", customer.ID),
		db.Eq("ChatChannel.platform", configs.PlatformWhatsApp),
		db.Eq("ChatChannel.is_active", true),
		db.IsNotNull("cs_conversations.customer_last_message_id"),
		db.Or(
			db.IsNull("Conversation.closed_at"),
			db.Eq("Conversation.closed_at", 0),
		),
	))
	if err != nil {
		return nil, fmt.Errorf("error querying active sessions: %w", err)
	}

	if len(conversations) == 0 {
		return nil, nil // No active session found
	}

	// Return the most recent active session (first one due to ordering)
	activeConversation := &conversations[0]

	// Validate that the channel still exists and is active
	if activeConversation.ChatChannel == nil {
		return nil, fmt.Errorf("channel not found for active session")
	}

	if activeConversation.ChatChannel.Platform.Get() != configs.PlatformWhatsApp {
		return nil, fmt.Errorf("channel is not WhatsApp platform")
	}

	if !activeConversation.ChatChannel.IsActive.Get() {
		return nil, fmt.Errorf("channel is not active")
	}

	return &activeSessionInfo{
		chat:    activeConversation,
		channel: activeConversation.ChatChannel,
	}, nil
}

// Helper function to validate message type
func isValidMessageType(messageType string) bool {
	validTypes := []string{
		configs.MessageTypeText,
		configs.MessageTypeImage,
		configs.MessageTypeVideo,
		configs.MessageTypeAudio,
		configs.MessageTypeFile,
	}

	for _, validType := range validTypes {
		if messageType == validType {
			return true
		}
	}
	return false
}

// Helper function to validate message content
func validateMessageContent(req *SendMessageRequest) error {
	switch req.MessageType {
	case configs.MessageTypeText:
		if strings.TrimSpace(req.Text) == "" {
			return fmt.Errorf("text message requires non-empty text content")
		}

	case configs.MessageTypeImage, configs.MessageTypeVideo, configs.MessageTypeAudio, configs.MessageTypeFile:
		if req.MediaContent == "" {
			return fmt.Errorf("%s message requires media_content (base64 encoded)", req.MessageType)
		}
		if req.Filename == "" {
			return fmt.Errorf("%s message requires filename", req.MessageType)
		}

	default:
		return fmt.Errorf("unsupported message type: %s", req.MessageType)
	}

	return nil
}

// Helper function to create message from request
func createMessageFromRequest(db *metaorm.DB, req *SendMessageRequest, chat *entities.CsConversation, channel *entities.ChatChannel, customer *relationshipEntities.Customer, workspaceId uint) (*entities.Message, error) {
	// Create chat manager to use the proper initialization pattern
	chatManager := chat_service.NewChatManager(db)

	// Use the same pattern as cs_chat_service which works successfully
	var message *entities.Message
	var err error

	switch req.MessageType {
	case configs.MessageTypeText:
		message, err = chatManager.InitiateTextMessage(chat.ConversationId.Get(), req.Text)
	case configs.MessageTypeImage:
		message, err = chatManager.InitiateImageMessage(chat.ConversationId.Get(), req.Filename, req.MediaContent)
		// Use text as caption if provided
		if req.Text != "" {
			message.Text.Set(req.Text)
		}
	case configs.MessageTypeVideo:
		message, err = chatManager.InitiateVideoMessage(chat.ConversationId.Get(), req.Filename, req.MediaContent)
		// Use text as caption if provided
		if req.Text != "" {
			message.Text.Set(req.Text)
		}
	case configs.MessageTypeAudio:
		message, err = chatManager.InitiateAudioMessage(chat.ConversationId.Get(), req.Filename, req.MediaContent)
		// Use text as caption if provided
		if req.Text != "" {
			message.Text.Set(req.Text)
		}
	case configs.MessageTypeFile:
		message, err = chatManager.InitiateFileMessage(chat.ConversationId.Get(), req.Filename, req.MediaContent)
		// Use text as caption if provided
		if req.Text != "" {
			message.Text.Set(req.Text)
		}
	default:
		return nil, fmt.Errorf("unsupported message type: %s", req.MessageType)
	}

	if err != nil {
		return nil, fmt.Errorf("error initiating message: %w", err)
	}

	// Set additional fields following the successful pattern
	message.Timestamp.Set(time.Now().Unix())
	message.Status.Set(configs.MessageStatusPending)
	message.PlatformSenderId.Set(channel.PhoneNumberID.Get())
	message.PlatformRecipientId.Set(customer.WhatsappId.Get())

	// Save message
	message, err = message.Repo(db, workspaceId).Save(message)
	if err != nil {
		return nil, fmt.Errorf("error saving message: %w", err)
	}

	return message, nil
}
