package actions

import (
	"fmt"
	"strings"
	"time"

	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/whatsapp_service"
	relationshipEntities "github.com/metadiv-tech/mod_relationship/entities"
	"github.com/metadiv-tech/workflow"
)

type SendTemplateRequest struct {
	CustomerId       uint   `json:"customer_id"`
	TemplateId       uint   `json:"template_id"`
	TemplateLanguage string `json:"template_language"`

	// Optional template parameters
	HeaderParams []entities.WhatsappTemplateParam `json:"header_params,omitempty"`
	BodyParams   []entities.WhatsappTemplateParam `json:"body_params,omitempty"`
}

type SendTemplateResponse struct {
	Success   bool   `json:"success"`
	MessageId uint   `json:"message_id,omitempty"`
	Error     string `json:"error,omitempty"`
}

var SendTemplate = workflow.NewAction[SendTemplateRequest, SendTemplateResponse]("send.template").
	WithCategory("WhatsApp").
	WithName("sendTemplate").
	WithFunction(func(ctx *workflow.Context, input SendTemplateRequest) (SendTemplateResponse, error) {
		db := ctx.DB
		workspaceId := ctx.WorkspaceId

		// Set default language if not provided
		if input.TemplateLanguage == "" {
			input.TemplateLanguage = "en"
		}

		// 1. Find and validate template exists
		template, err := new(entities.WhatsappTemplate).Repo(db.Preload("Contents").Preload("Channel"), workspaceId).FindOne(db.Eq("id", input.TemplateId))
		if err != nil {
			return SendTemplateResponse{Success: false, Error: fmt.Sprintf("Error finding template: %v", err)}, nil
		}
		if template == nil {
			return SendTemplateResponse{Success: false, Error: "Template not found"}, nil
		}

		// Find template content for the specified language
		var templateContent *entities.WhatsappTemplateContent
		for _, content := range template.Contents {
			if content.Language.Get() == input.TemplateLanguage {
				templateContent = &content
				break
			}
		}
		if templateContent == nil {
			return SendTemplateResponse{Success: false, Error: fmt.Sprintf("Template content not found for language: %s", input.TemplateLanguage)}, nil
		}

		// Ensure template has associated channel
		if template.Channel == nil {
			return SendTemplateResponse{Success: false, Error: "Template has no associated chat channel"}, nil
		}

		// Validate channel is WhatsApp platform
		if template.Channel.Platform.Get() != configs.PlatformWhatsApp {
			return SendTemplateResponse{Success: false, Error: "Template channel is not WhatsApp platform"}, nil
		}

		// 2. Find and validate customer exists
		customer, err := new(relationshipEntities.Customer).Repo(db, workspaceId).FindOne(db.Eq("id", input.CustomerId))
		if err != nil {
			return SendTemplateResponse{Success: false, Error: fmt.Sprintf("Error finding customer: %v", err)}, nil
		}
		if customer == nil {
			return SendTemplateResponse{Success: false, Error: "Customer not found"}, nil
		}

		// Ensure customer has WhatsApp ID
		if customer.WhatsappId.Get() == "" {
			return SendTemplateResponse{Success: false, Error: "Customer has no WhatsApp ID"}, nil
		}

		// 3. Find or create conversation for this customer and channel
		chat, err := findOrCreateCsConversation(db, customer, template.Channel, workspaceId)
		if err != nil {
			return SendTemplateResponse{Success: false, Error: fmt.Sprintf("Error creating conversation: %v", err)}, nil
		}

		// 4. Create message with template data
		message := new(entities.Message)
		message.UUID.Generate()
		message.ConversationId.Set(chat.ConversationId.Get())
		message.MessageType.Set(configs.MessageTypeTemplate)
		message.Timestamp.Set(time.Now().Unix())
		message.Status.Set(configs.MessageStatusPending)
		message.PlatformSenderId.Set(template.Channel.PhoneNumberID.Get())
		message.PlatformRecipientId.Set(customer.WhatsappId.Get())
		message.TemplateName.Set(template.Name.Get())
		message.TemplateLanguage.Set(input.TemplateLanguage)

		// 5. Process template parameters with customer data substitution
		header := processTemplateContent(templateContent.HeaderContent.Get(), input.HeaderParams, customer)
		body := processTemplateContent(templateContent.BodyContent.Get(), input.BodyParams, customer)
		footer := processTemplateContent(templateContent.FooterContent.Get(), nil, customer)

		message.TemplateHeader.Set(header)
		message.TemplateBody.Set(body)
		message.TemplateFooter.Set(footer)

		// Set template parameters
		if len(input.HeaderParams) > 0 {
			message.TemplateHeaderParams.Set(input.HeaderParams)
		}
		if len(input.BodyParams) > 0 {
			message.TemplateBodyParams.Set(input.BodyParams)
		}

		// 6. Save message
		message, err = message.Repo(db, workspaceId).Save(message)
		if err != nil {
			return SendTemplateResponse{Success: false, Error: fmt.Sprintf("Error saving message: %v", err)}, nil
		}

		// 7. Send message using platform sender
		platformSender := whatsapp_service.NewPlatformSender(db)
		platformSender.SendMessageAsync(message, template.Channel, workspaceId)

		return SendTemplateResponse{Success: true, MessageId: message.ID}, nil
	}).Build()

// Helper function to find or create CS conversation
func findOrCreateCsConversation(db *metaorm.DB, customer *relationshipEntities.Customer, channel *entities.ChatChannel, workspaceId uint) (*entities.CsConversation, error) {
	// Try to find existing conversation
	chat, err := new(entities.CsConversation).Repo(db, workspaceId).FindOne(db.And(
		db.Eq("customer_id", customer.ID),
		db.Eq("chat_channel_id", channel.ID),
	))
	if err != nil {
		return nil, fmt.Errorf("error querying conversation: %w", err)
	}

	if chat != nil {
		return chat, nil
	}

	// Create new conversation
	conv := new(entities.Conversation)
	conv.UUID.Generate()
	conv.ChatChannelId.Set(channel.ID)
	conv.Type.Set(configs.ConversationTypeDirect)
	conv, err = conv.Repo(db).Save(conv)
	if err != nil {
		return nil, fmt.Errorf("error creating conversation: %w", err)
	}

	// Create CS conversation
	chat = new(entities.CsConversation)
	chat.CustomerId.Set(customer.ID)
	chat.ChatChannelId.Set(channel.ID)
	chat.ConversationId.Set(conv.ID)
	chat.UnreadCount.Set(0)
	chat.Pinned.Set(false)

	chat, err = chat.Repo(db, workspaceId).Save(chat)
	if err != nil {
		return nil, fmt.Errorf("error saving cs conversation: %w", err)
	}

	return chat, nil
}

// Helper function to process template content with parameters and customer data
func processTemplateContent(content string, params []entities.WhatsappTemplateParam, customer *relationshipEntities.Customer) string {
	processed := content

	// Apply custom parameters first
	for _, param := range params {
		placeholder := "{{" + param.Key + "}}"
		processed = strings.ReplaceAll(processed, placeholder, param.Value)
	}

	// Apply customer data substitutions
	processed = strings.ReplaceAll(processed, "{{display_name}}", customer.DisplayName.Get())
	processed = strings.ReplaceAll(processed, "{{customer_email}}", customer.Email.Get())
	processed = strings.ReplaceAll(processed, "{{customer_phone}}", customer.Phone.Get())
	processed = strings.ReplaceAll(processed, "{{customer_name}}", customer.DisplayName.Get())

	return processed
}
