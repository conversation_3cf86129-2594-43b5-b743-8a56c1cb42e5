package mod_chat

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/mod_chat/endpoints/cs_chat"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_relationship"
)

var ModuleCsChat = metagin.NewModule("cs_chat", "v1", func(m *metagin.Module) {
	m.RegisterMigration(
		&entities.CsConversation{},
		&entities.CsMessage{},
	)
	m.Register<PERSON>andler(
		cs_chat.ApiCsConversationCreate,
		cs_chat.ApiCsConversationUpdate,
		cs_chat.ApiCsConversationDelete,
		cs_chat.ApiCsConversationGet,
		cs_chat.WsCsConversationList,

		cs_chat.ApiCsMessageSend,
		cs_chat.CronCsMessageSend,
		cs_chat.WsCsMessageList,
	)
}, ModuleChat, mod_relationship.ModuleCustomer)
