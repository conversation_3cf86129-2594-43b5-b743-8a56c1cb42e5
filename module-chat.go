package mod_chat

import (
	"github.com/metadiv-tech/metagin"
	_ "github.com/metadiv-tech/mod_chat/actions"
	"github.com/metadiv-tech/mod_chat/endpoints/chat"
	"github.com/metadiv-tech/mod_chat/endpoints/instagram"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_relationship"
	"github.com/metadiv-tech/mod_utils"
)

var ModuleChat = metagin.NewModule("chat", "v1", func(m *metagin.Module) {
	m.RegisterMigration(
		&entities.ChatChannel{},
		&entities.Conversation{},
		&entities.Message{},
	)
	m.RegisterHandler(
		chat.ApiChatChannelCreate,
		chat.ApiChatChannelDelete,
		chat.ApiChatChannelGet,
		chat.ApiChatChannelList,
		chat.ApiChatChannelUpdate,

		chat.ApiWhatsappWebhook,
		chat.ApiWhatsappChallenge,
		chat.ApiChatChannelWebhookInfoGet,

		// Instagram endpoints
		instagram.ApiInstagramWebhook,
		instagram.ApiInstagramChallenge,
	)
}, mod_relationship.ModuleCustomer, mod_utils.ModuleSystemSetting)
